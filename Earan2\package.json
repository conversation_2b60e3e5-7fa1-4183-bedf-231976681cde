{"name": "earan-marketplace", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint", "test": "jest"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "expo": "^53.0.11", "expo-blur": "~14.1.5", "expo-camera": "~16.1.8", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.3", "expo-location": "~18.1.3", "expo-router": "~5.1.0", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-web-browser": "~14.1.6", "@expo-google-fonts/inter": "^0.2.3", "react-native-maps": "1.20.1", "react-native-reanimated": "~3.17.4", "react-native-gesture-handler": "~2.24.0", "react-native-country-picker-modal": "^2.0.0", "react-hook-form": "^7.48.2", "yup": "^1.4.0", "@hookform/resolvers": "^3.3.2", "dayjs": "^1.11.10", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "react-native-toast-message": "^2.1.7", "react-native-paystack-webview": "^4.1.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3", "jest": "^29.7.0", "@testing-library/react-native": "^12.4.2"}}