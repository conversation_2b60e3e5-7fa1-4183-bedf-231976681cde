import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { useSelector } from 'react-redux';
import { Calendar, Clock, MapPin, Phone, MessageSquare, Star } from 'lucide-react-native';
import { RootState } from '../../store';
import { BookingCard } from '../../components/molecules/BookingCard';
import { mockBookings } from '../../data/mockData';
import { theme } from '../../theme/colors';
import { BookingStatus } from '../../types';

const statusFilters: { key: BookingStatus | 'all'; label: string }[] = [
  { key: 'all', label: 'All' },
  { key: 'pending', label: 'Pending' },
  { key: 'confirmed', label: 'Confirmed' },
  { key: 'in_progress', label: 'Active' },
  { key: 'completed', label: 'Completed' },
];

export default function BookingsScreen() {
  const { user } = useSelector((state: RootState) => state.auth);
  const [selectedFilter, setSelectedFilter] = useState<BookingStatus | 'all'>('all');

  const filteredBookings = mockBookings.filter(booking => {
    if (selectedFilter === 'all') return true;
    return booking.status === selectedFilter;
  });

  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
      case 'pending':
        return theme.colors.warning[500];
      case 'confirmed':
        return theme.colors.primary[600];
      case 'in_progress':
        return theme.colors.secondary[600];
      case 'completed':
        return theme.colors.success[600];
      case 'cancelled':
      case 'disputed':
        return theme.colors.error[500];
      default:
        return theme.colors.gray[500];
    }
  };

  const getStatusText = (status: BookingStatus) => {
    switch (status) {
      case 'pending':
        return 'Pending Confirmation';
      case 'confirmed':
        return 'Confirmed';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'disputed':
        return 'Disputed';
      default:
        return status;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Bookings</Text>
        <Text style={styles.headerSubtitle}>
          {filteredBookings.length} booking{filteredBookings.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {/* Status Filters */}
      <View style={styles.filtersContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {statusFilters.map((filter) => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterChip,
                selectedFilter === filter.key && styles.filterChipActive,
              ]}
              onPress={() => setSelectedFilter(filter.key)}
            >
              <Text style={[
                styles.filterChipText,
                selectedFilter === filter.key && styles.filterChipTextActive,
              ]}>
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.bookingsList}>
        {filteredBookings.length > 0 ? (
          filteredBookings.map((booking) => (
            <BookingCard
              key={booking.id}
              booking={booking}
              onPress={() => console.log('Booking pressed:', booking.id)}
              onContact={() => console.log('Contact provider:', booking.provider.user.name)}
              onMessage={() => console.log('Message provider:', booking.provider.user.name)}
            />
          ))
        ) : (
          <View style={styles.emptyState}>
            <Calendar size={64} color={theme.colors.gray[400]} />
            <Text style={styles.emptyTitle}>No bookings found</Text>
            <Text style={styles.emptyDescription}>
              {selectedFilter === 'all' 
                ? "You haven't made any bookings yet"
                : `No ${selectedFilter} bookings at the moment`
              }
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.gray[50],
  },
  header: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  headerTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: theme.typography.fontSize['2xl'],
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.xs,
  },
  headerSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[600],
  },
  filtersContainer: {
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  filterChip: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    marginLeft: theme.spacing.lg,
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.gray[100],
  },
  filterChipActive: {
    backgroundColor: theme.colors.primary[600],
  },
  filterChipText: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[700],
  },
  filterChipTextActive: {
    color: theme.colors.white,
  },
  bookingsList: {
    flex: 1,
    padding: theme.spacing.lg,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xxl * 2,
  },
  emptyTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.gray[900],
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.sm,
  },
  emptyDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.gray[600],
    textAlign: 'center',
    maxWidth: 250,
  },
});