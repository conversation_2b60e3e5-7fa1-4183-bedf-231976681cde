import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { router } from 'expo-router';
import { useDispatch } from 'react-redux';
import { User, Mail, MapPin } from 'lucide-react-native';
import { Button } from '../../components/atoms/Button';
import { Input } from '../../components/atoms/Input';
import { setUser } from '../../store/slices/authSlice';
import { theme } from '../../theme/colors';

export default function ProfileSetupScreen() {
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    location: '',
  });
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCompleteSetup = async () => {
    if (!formData.name.trim() || !formData.email.trim()) return;
    
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const user = {
        id: '1',
        name: formData.name,
        email: formData.email,
        phone: '+233241234567',
        role: 'customer' as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      dispatch(setUser({ user, token: 'mock-jwt-token' }));
      setLoading(false);
      router.replace('/(tabs)');
    }, 2000);
  };

  const isFormValid = formData.name.trim() && formData.email.trim();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <Text style={styles.title}>Complete your profile</Text>
        <Text style={styles.description}>
          Tell us a bit about yourself to get started
        </Text>

        <View style={styles.form}>
          <Input
            label="Full Name"
            placeholder="Enter your full name"
            value={formData.name}
            onChangeText={(value) => handleInputChange('name', value)}
            leftIcon={<User size={20} color={theme.colors.gray[400]} />}
          />

          <Input
            label="Email Address"
            placeholder="Enter your email"
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            keyboardType="email-address"
            autoCapitalize="none"
            leftIcon={<Mail size={20} color={theme.colors.gray[400]} />}
          />

          <Input
            label="Location (Optional)"
            placeholder="Enter your location"
            value={formData.location}
            onChangeText={(value) => handleInputChange('location', value)}
            leftIcon={<MapPin size={20} color={theme.colors.gray[400]} />}
          />
        </View>

        <Button
          title="Complete Setup"
          onPress={handleCompleteSetup}
          loading={loading}
          disabled={!isFormValid}
          size="lg"
        />

        <TouchableOpacity onPress={() => router.replace('/(tabs)')}>
          <Text style={styles.skipText}>Skip for now</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  content: {
    flexGrow: 1,
    padding: theme.spacing.lg,
    justifyContent: 'center',
  },
  title: {
    fontFamily: 'Inter-Bold',
    fontSize: theme.typography.fontSize['2xl'],
    color: theme.colors.gray[900],
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  description: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.gray[600],
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing.xl,
  },
  form: {
    marginBottom: theme.spacing.xl,
  },
  skipText: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[500],
    textAlign: 'center',
    marginTop: theme.spacing.lg,
  },
});