import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft, Phone } from 'lucide-react-native';
import { Button } from '../../components/atoms/Button';
import { Input } from '../../components/atoms/Input';
import { theme } from '../../theme/colors';

export default function PhoneScreen() {
  const [phone, setPhone] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSendOTP = async () => {
    if (!phone.trim()) return;
    
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      router.push('/auth/otp');
    }, 2000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={theme.colors.gray[700]} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Enter Phone Number</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Phone size={48} color={theme.colors.primary[600]} />
        </View>

        <Text style={styles.title}>Verify your phone number</Text>
        <Text style={styles.description}>
          We'll send you a verification code to confirm your phone number
        </Text>

        <Input
          label="Phone Number"
          placeholder="+233 24 123 4567"
          value={phone}
          onChangeText={setPhone}
          keyboardType="phone-pad"
          leftIcon={<Phone size={20} color={theme.colors.gray[400]} />}
        />

        <Button
          title="Send Verification Code"
          onPress={handleSendOTP}
          loading={loading}
          disabled={!phone.trim()}
          size="lg"
        />

        <Text style={styles.terms}>
          By continuing, you agree to our{' '}
          <Text style={styles.link}>Terms of Service</Text> and{' '}
          <Text style={styles.link}>Privacy Policy</Text>
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  backButton: {
    marginRight: theme.spacing.md,
  },
  headerTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.gray[900],
  },
  content: {
    flex: 1,
    padding: theme.spacing.lg,
    justifyContent: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  title: {
    fontFamily: 'Inter-Bold',
    fontSize: theme.typography.fontSize['2xl'],
    color: theme.colors.gray[900],
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  description: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.gray[600],
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing.xl,
  },
  terms: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[500],
    textAlign: 'center',
    marginTop: theme.spacing.lg,
    lineHeight: 20,
  },
  link: {
    color: theme.colors.primary[600],
    textDecorationLine: 'underline',
  },
});