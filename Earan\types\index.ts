export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  role: 'customer' | 'provider';
  location?: GeoPoint;
  createdAt: string;
  updatedAt: string;
}

export interface GeoPoint {
  latitude: number;
  longitude: number;
  address?: string;
}

export interface Service {
  id: string;
  name: string;
  description: string;
  category: ServiceCategory;
  image?: string;
  basePrice: number;
  duration: number; // in minutes
}

export interface ServiceCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
}

export interface ServiceProvider {
  id: string;
  user: User;
  services: Service[];
  location: GeoPoint;
  rating: number;
  reviewCount: number;
  availability: Schedule;
  pricing: PriceRange;
  portfolio: string[];
  isVerified: boolean;
  isAvailable: boolean;
}

export interface Schedule {
  [key: string]: {
    isAvailable: boolean;
    timeSlots: TimeSlot[];
  };
}

export interface TimeSlot {
  start: string;
  end: string;
  isBooked: boolean;
}

export interface PriceRange {
  min: number;
  max: number;
  currency: string;
}

export interface Booking {
  id: string;
  customer: User;
  provider: ServiceProvider;
  service: Service;
  status: BookingStatus;
  scheduledDate: string;
  scheduledTime: string;
  location: GeoPoint;
  payment?: Payment;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export type BookingStatus = 
  | 'pending' 
  | 'confirmed' 
  | 'in_progress' 
  | 'completed' 
  | 'cancelled' 
  | 'disputed';

export interface Payment {
  id: string;
  provider: 'paystack';
  amount: number;
  status: PaymentStatus;
  reference: string;
  method: PaymentMethod;
  createdAt: string;
}

export type PaymentStatus = 'pending' | 'success' | 'failed' | 'refunded';
export type PaymentMethod = 'card' | 'bank_transfer' | 'mobile_money';

export interface Review {
  id: string;
  booking: string;
  reviewer: User;
  reviewee: User;
  rating: number;
  comment?: string;
  createdAt: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  token?: string;
}

export interface AppState {
  auth: AuthState;
  userLocation: GeoPoint | null;
  selectedRole: 'customer' | 'provider' | null;
}