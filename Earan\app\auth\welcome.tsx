import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  SafeAreaView,
} from 'react-native';
import { router } from 'expo-router';
import { Button } from '../../components/atoms/Button';
import { theme } from '../../theme/colors';

export default function WelcomeScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.hero}>
          <Image
            source={{
              uri: 'https://images.pexels.com/photos/5691621/pexels-photo-5691621.jpeg?auto=compress&cs=tinysrgb&w=800',
            }}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.overlay} />
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>Earan</Text>
            <Text style={styles.heroSubtitle}>
              Connect with skilled artisans in Ghana
            </Text>
          </View>
        </View>

        <View style={styles.bottom}>
          <Text style={styles.title}>Find Quality Services</Text>
          <Text style={styles.description}>
            Get connected with verified professionals for all your home and business needs. 
            From plumbing to carpentry, cleaning to electrical work.
          </Text>

          <View style={styles.buttons}>
            <Button
              title="Get Started"
              onPress={() => router.push('/auth/phone')}
              size="lg"
            />
            <Button
              title="I'm a Service Provider"
              onPress={() => router.push('/auth/phone')}
              variant="outline"
              size="lg"
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  content: {
    flex: 1,
  },
  hero: {
    flex: 0.6,
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  heroContent: {
    position: 'absolute',
    bottom: theme.spacing.xxl,
    left: theme.spacing.lg,
    right: theme.spacing.lg,
  },
  heroTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: theme.typography.fontSize['4xl'],
    color: theme.colors.white,
    marginBottom: theme.spacing.sm,
  },
  heroSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.white,
    opacity: 0.9,
  },
  bottom: {
    flex: 0.4,
    padding: theme.spacing.lg,
    justifyContent: 'center',
  },
  title: {
    fontFamily: 'Inter-Bold',
    fontSize: theme.typography.fontSize['2xl'],
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.md,
    textAlign: 'center',
  },
  description: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.gray[600],
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing.xl,
  },
  buttons: {
    gap: theme.spacing.md,
  },
});