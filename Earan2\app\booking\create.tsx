import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Calendar, Clock, MapPin, CreditCard } from 'lucide-react-native';
import { colors, spacing, typography, borderRadius } from '@/constants/theme';
import { MOCK_PROVIDERS } from '@/data/mockProviders';
import { SERVICE_CATEGORIES } from '@/constants/services';
import Button from '@/components/common/Button';
import dayjs from 'dayjs';

interface TimeSlot {
  time: string;
  available: boolean;
}

const timeSlots: TimeSlot[] = [
  { time: '09:00', available: true },
  { time: '10:00', available: false },
  { time: '11:00', available: true },
  { time: '12:00', available: true },
  { time: '14:00', available: true },
  { time: '15:00', available: false },
  { time: '16:00', available: true },
  { time: '17:00', available: true },
];

export default function CreateBookingScreen() {
  const { providerId, serviceId } = useLocalSearchParams<{ 
    providerId: string; 
    serviceId?: string; 
  }>();
  
  const [provider, setProvider] = useState(MOCK_PROVIDERS.find(p => p.id === providerId));
  const [selectedDate, setSelectedDate] = useState(dayjs().format('YYYY-MM-DD'));
  const [selectedTime, setSelectedTime] = useState('');
  const [selectedService, setSelectedService] = useState('');
  const [specialRequirements, setSpecialRequirements] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (serviceId) {
      setSelectedService(serviceId);
    }
  }, [serviceId]);

  const getNextDays = (count: number) => {
    const days = [];
    for (let i = 0; i < count; i++) {
      days.push(dayjs().add(i, 'day'));
    }
    return days;
  };

  const availableDays = getNextDays(7);

  const handleBooking = async () => {
    if (!selectedDate || !selectedTime || !selectedService) {
      Alert.alert('Error', 'Please select date, time, and service');
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      const bookingId = Math.random().toString(36).substr(2, 9);
      router.replace({
        pathname: '/booking/confirmation',
        params: { bookingId }
      });
    }, 2000);
  };

  const getServicePrice = (serviceId: string) => {
    const pricing = provider?.pricing.find(p => p.subcategoryId === serviceId);
    return pricing?.basePrice || 100;
  };

  const selectedServicePrice = selectedService ? getServicePrice(selectedService) : 0;
  const platformFee = selectedServicePrice * 0.05; // 5% platform fee
  const totalPrice = selectedServicePrice + platformFee;

  if (!provider) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Provider not found</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft color={colors.gray[600]} size={24} />
          </TouchableOpacity>
          <Text style={styles.title}>Book Service</Text>
        </View>

        {/* Provider Info */}
        <View style={styles.providerSection}>
          <Text style={styles.sectionTitle}>Service Provider</Text>
          <View style={styles.providerInfo}>
            <Text style={styles.providerName}>{provider.name}</Text>
            <Text style={styles.providerTitle}>{provider.professionalTitle}</Text>
          </View>
        </View>

        {/* Service Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select Service</Text>
          {provider.serviceCategories.map(categoryId => {
            const category = SERVICE_CATEGORIES.find(c => c.id === categoryId);
            return category?.subcategories.map(subcategory => (
              <TouchableOpacity
                key={subcategory.id}
                style={[
                  styles.serviceOption,
                  selectedService === subcategory.id && styles.selectedServiceOption
                ]}
                onPress={() => setSelectedService(subcategory.id)}
              >
                <View style={styles.serviceInfo}>
                  <Text style={styles.serviceName}>{subcategory.name}</Text>
                  <Text style={styles.serviceDescription}>{subcategory.description}</Text>
                </View>
                <Text style={styles.servicePrice}>
                  GHS {getServicePrice(subcategory.id)}
                </Text>
              </TouchableOpacity>
            ));
          })}
        </View>

        {/* Date Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select Date</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.dateContainer}>
              {availableDays.map((day, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.dateOption,
                    selectedDate === day.format('YYYY-MM-DD') && styles.selectedDateOption
                  ]}
                  onPress={() => setSelectedDate(day.format('YYYY-MM-DD'))}
                >
                  <Text style={[
                    styles.dayText,
                    selectedDate === day.format('YYYY-MM-DD') && styles.selectedDayText
                  ]}>
                    {day.format('ddd')}
                  </Text>
                  <Text style={[
                    styles.dateText,
                    selectedDate === day.format('YYYY-MM-DD') && styles.selectedDateText
                  ]}>
                    {day.format('DD')}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Time Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select Time</Text>
          <View style={styles.timeGrid}>
            {timeSlots.map((slot, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.timeSlot,
                  !slot.available && styles.unavailableTimeSlot,
                  selectedTime === slot.time && styles.selectedTimeSlot
                ]}
                onPress={() => slot.available && setSelectedTime(slot.time)}
                disabled={!slot.available}
              >
                <Text style={[
                  styles.timeText,
                  !slot.available && styles.unavailableTimeText,
                  selectedTime === slot.time && styles.selectedTimeText
                ]}>
                  {slot.time}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Special Requirements */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Special Requirements (Optional)</Text>
          <TextInput
            style={styles.textArea}
            placeholder="Any special instructions or requirements..."
            value={specialRequirements}
            onChangeText={setSpecialRequirements}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        {/* Price Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Price Summary</Text>
          <View style={styles.priceBreakdown}>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Service Fee</Text>
              <Text style={styles.priceValue}>GHS {selectedServicePrice}</Text>
            </View>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Platform Fee</Text>
              <Text style={styles.priceValue}>GHS {platformFee.toFixed(2)}</Text>
            </View>
            <View style={[styles.priceRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>GHS {totalPrice.toFixed(2)}</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom CTA */}
      <View style={styles.bottomSection}>
        <Button
          variant="primary"
          size="lg"
          onPress={handleBooking}
          loading={isLoading}
          disabled={!selectedDate || !selectedTime || !selectedService}
          fullWidth
        >
          Book Now - GHS {totalPrice.toFixed(2)}
        </Button>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    gap: spacing.md,
  },
  backButton: {
    padding: spacing.sm,
  },
  title: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    flex: 1,
  },
  providerSection: {
    paddingHorizontal: spacing.xl,
    paddingBottom: spacing.xl,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  providerInfo: {
    backgroundColor: colors.primary[50],
    padding: spacing.lg,
    borderRadius: borderRadius.md,
  },
  providerName: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  providerTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
  },
  section: {
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xl,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.lg,
  },
  serviceOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.gray[200],
    marginBottom: spacing.md,
  },
  selectedServiceOption: {
    borderColor: colors.primary[600],
    backgroundColor: colors.primary[50],
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  serviceDescription: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  servicePrice: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.primary[600],
  },
  dateContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    paddingRight: spacing.xl,
  },
  dateOption: {
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.gray[200],
    minWidth: 60,
  },
  selectedDateOption: {
    borderColor: colors.primary[600],
    backgroundColor: colors.primary[600],
  },
  dayText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Medium',
    color: colors.gray[600],
    marginBottom: spacing.xs,
  },
  selectedDayText: {
    color: colors.white,
  },
  dateText: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  selectedDateText: {
    color: colors.white,
  },
  timeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  timeSlot: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.gray[200],
    minWidth: 80,
    alignItems: 'center',
  },
  selectedTimeSlot: {
    borderColor: colors.primary[600],
    backgroundColor: colors.primary[600],
  },
  unavailableTimeSlot: {
    borderColor: colors.gray[100],
    backgroundColor: colors.gray[50],
  },
  timeText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  selectedTimeText: {
    color: colors.white,
  },
  unavailableTimeText: {
    color: colors.gray[400],
  },
  textArea: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[900],
    minHeight: 100,
  },
  priceBreakdown: {
    backgroundColor: colors.gray[50],
    borderRadius: borderRadius.md,
    padding: spacing.lg,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    paddingTop: spacing.md,
    marginBottom: 0,
  },
  priceLabel: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
  },
  priceValue: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  totalLabel: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  totalValue: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-Bold',
    color: colors.primary[600],
  },
  bottomSection: {
    padding: spacing.xl,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    backgroundColor: colors.white,
  },
});