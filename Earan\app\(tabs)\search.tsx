import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Search, Filter, MapPin, Star } from 'lucide-react-native';
import { Input } from '../../components/atoms/Input';
import { ServiceCard } from '../../components/molecules/ServiceCard';
import { mockProviders, mockCategories } from '../../data/mockData';
import { theme } from '../../theme/colors';

export default function SearchScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const filteredProviders = mockProviders.filter(provider => {
    const matchesSearch = provider.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         provider.services.some(service => 
                           service.name.toLowerCase().includes(searchQuery.toLowerCase())
                         );
    
    const matchesCategory = !selectedCategory || 
                           provider.services.some(service => service.category.id === selectedCategory);
    
    return matchesSearch && matchesCategory;
  });

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Find Services</Text>
        <TouchableOpacity style={styles.filterButton}>
          <Filter size={20} color={theme.colors.gray[700]} />
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Input
            placeholder="Search for services or providers..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            leftIcon={<Search size={20} color={theme.colors.gray[400]} />}
          />
        </View>

        {/* Location Filter */}
        <View style={styles.locationFilter}>
          <MapPin size={16} color={theme.colors.primary[600]} />
          <Text style={styles.locationText}>Within 10km of your location</Text>
          <TouchableOpacity>
            <Text style={styles.changeText}>Change</Text>
          </TouchableOpacity>
        </View>

        {/* Category Filters */}
        <View style={styles.filtersContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[
                styles.filterChip,
                !selectedCategory && styles.filterChipActive,
              ]}
              onPress={() => setSelectedCategory(null)}
            >
              <Text style={[
                styles.filterChipText,
                !selectedCategory && styles.filterChipTextActive,
              ]}>
                All
              </Text>
            </TouchableOpacity>
            {mockCategories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.filterChip,
                  selectedCategory === category.id && styles.filterChipActive,
                ]}
                onPress={() => setSelectedCategory(category.id)}
              >
                <Text style={[
                  styles.filterChipText,
                  selectedCategory === category.id && styles.filterChipTextActive,
                ]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Results */}
        <View style={styles.resultsContainer}>
          <View style={styles.resultsHeader}>
            <Text style={styles.resultsText}>
              {filteredProviders.length} providers found
            </Text>
            <TouchableOpacity style={styles.sortButton}>
              <Star size={16} color={theme.colors.gray[600]} />
              <Text style={styles.sortText}>Rating</Text>
            </TouchableOpacity>
          </View>

          {filteredProviders.map((provider) => (
            <ServiceCard
              key={provider.id}
              provider={provider}
              onPress={() => console.log('Provider pressed:', provider.user.name)}
            />
          ))}

          {filteredProviders.length === 0 && (
            <View style={styles.emptyState}>
              <Text style={styles.emptyTitle}>No providers found</Text>
              <Text style={styles.emptyDescription}>
                Try adjusting your search criteria or location
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.gray[50],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  headerTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.gray[900],
  },
  filterButton: {
    padding: theme.spacing.sm,
  },
  searchContainer: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
  },
  locationFilter: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  locationText: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[700],
    marginLeft: theme.spacing.sm,
    flex: 1,
  },
  changeText: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.primary[600],
  },
  filtersContainer: {
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  filterChip: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    marginLeft: theme.spacing.lg,
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.gray[100],
  },
  filterChipActive: {
    backgroundColor: theme.colors.primary[600],
  },
  filterChipText: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[700],
  },
  filterChipTextActive: {
    color: theme.colors.white,
  },
  resultsContainer: {
    padding: theme.spacing.lg,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  resultsText: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[600],
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortText: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[600],
    marginLeft: theme.spacing.xs,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xxl,
  },
  emptyTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.sm,
  },
  emptyDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.gray[600],
    textAlign: 'center',
  },
});