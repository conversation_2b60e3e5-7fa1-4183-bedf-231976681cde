import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { GeoPoint } from '../../types';

interface AppState {
  userLocation: GeoPoint | null;
  selectedRole: 'customer' | 'provider' | null;
  isLocationLoading: boolean;
}

const initialState: AppState = {
  userLocation: null,
  selectedRole: null,
  isLocationLoading: false,
};

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setUserLocation: (state, action: PayloadAction<GeoPoint>) => {
      state.userLocation = action.payload;
      state.isLocationLoading = false;
    },
    setLocationLoading: (state, action: PayloadAction<boolean>) => {
      state.isLocationLoading = action.payload;
    },
    setSelectedRole: (state, action: PayloadAction<'customer' | 'provider'>) => {
      state.selectedRole = action.payload;
    },
  },
});

export const { setUserLocation, setLocationLoading, setSelectedRole } = appSlice.actions;
export default appSlice.reducer;