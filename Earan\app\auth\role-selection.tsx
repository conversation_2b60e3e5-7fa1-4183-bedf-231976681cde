import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { router } from 'expo-router';
import { useDispatch } from 'react-redux';
import { Users, Wrench } from 'lucide-react-native';
import { Button } from '../../components/atoms/Button';
import { setSelectedRole } from '../../store/slices/appSlice';
import { theme } from '../../theme/colors';

export default function RoleSelectionScreen() {
  const dispatch = useDispatch();
  const [selectedRole, setSelectedRoleState] = useState<'customer' | 'provider' | null>(null);

  const handleRoleSelect = (role: 'customer' | 'provider') => {
    setSelectedRoleState(role);
  };

  const handleContinue = () => {
    if (selectedRole) {
      dispatch(setSelectedRole(selectedRole));
      router.push('/auth/profile-setup');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>How will you use Earan?</Text>
        <Text style={styles.description}>
          Choose your role to get the best experience tailored for you
        </Text>

        <View style={styles.roleContainer}>
          <TouchableOpacity
            style={[
              styles.roleCard,
              selectedRole === 'customer' && styles.roleCardSelected,
            ]}
            onPress={() => handleRoleSelect('customer')}
          >
            <View style={styles.roleIcon}>
              <Users size={32} color={theme.colors.primary[600]} />
            </View>
            <Text style={styles.roleTitle}>I'm a Customer</Text>
            <Text style={styles.roleDescription}>
              Looking for skilled professionals to help with my projects
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.roleCard,
              selectedRole === 'provider' && styles.roleCardSelected,
            ]}
            onPress={() => handleRoleSelect('provider')}
          >
            <View style={styles.roleIcon}>
              <Wrench size={32} color={theme.colors.secondary[600]} />
            </View>
            <Text style={styles.roleTitle}>I'm a Service Provider</Text>
            <Text style={styles.roleDescription}>
              Ready to offer my skills and services to customers
            </Text>
          </TouchableOpacity>
        </View>

        <Button
          title="Continue"
          onPress={handleContinue}
          disabled={!selectedRole}
          size="lg"
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  content: {
    flex: 1,
    padding: theme.spacing.lg,
    justifyContent: 'center',
  },
  title: {
    fontFamily: 'Inter-Bold',
    fontSize: theme.typography.fontSize['2xl'],
    color: theme.colors.gray[900],
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  description: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.gray[600],
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing.xl,
  },
  roleContainer: {
    gap: theme.spacing.lg,
    marginBottom: theme.spacing.xl,
  },
  roleCard: {
    padding: theme.spacing.lg,
    borderWidth: 2,
    borderColor: theme.colors.gray[200],
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    backgroundColor: theme.colors.white,
  },
  roleCardSelected: {
    borderColor: theme.colors.primary[600],
    backgroundColor: theme.colors.primary[50],
  },
  roleIcon: {
    marginBottom: theme.spacing.md,
  },
  roleTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.sm,
  },
  roleDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[600],
    textAlign: 'center',
    lineHeight: 20,
  },
});