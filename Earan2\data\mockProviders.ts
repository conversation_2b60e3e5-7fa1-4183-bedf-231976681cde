import { ServiceProvider, Location } from '@/types';
import { GHANA_LOCATIONS } from '@/constants/services';

// Helper function to generate random coordinates within Ghana
const generateRandomLocation = (baseCity: typeof GHANA_LOCATIONS[0]): Location => {
  const [baseLng, baseLat] = baseCity.coordinates;
  const randomNeighborhood = baseCity.neighborhoods[Math.floor(Math.random() * baseCity.neighborhoods.length)];
  
  return {
    address: `${randomNeighborhood}, ${baseCity.city}`,
    coordinates: [
      baseLng + (Math.random() - 0.5) * 0.1, // ~5km radius
      baseLat + (Math.random() - 0.5) * 0.1,
    ],
    city: baseCity.city,
    region: baseCity.region,
    country: 'Ghana',
  };
};

// Mock portfolio images (using Pexels)
const portfolioImages = {
  plumbing: [
    'https://images.pexels.com/photos/8413299/pexels-photo-8413299.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/5691659/pexels-photo-5691659.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/6419121/pexels-photo-6419121.jpeg?auto=compress&cs=tinysrgb&w=800',
  ],
  electrical: [
    'https://images.pexels.com/photos/257736/pexels-photo-257736.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/162553/keys-workshop-mechanic-tools-162553.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/5691525/pexels-photo-5691525.jpeg?auto=compress&cs=tinysrgb&w=800',
  ],
  carpentry: [
    'https://images.pexels.com/photos/175709/pexels-photo-175709.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/1251176/pexels-photo-1251176.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/1370295/pexels-photo-1370295.jpeg?auto=compress&cs=tinysrgb&w=800',
  ],
  cleaning: [
    'https://images.pexels.com/photos/4239146/pexels-photo-4239146.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/4239119/pexels-photo-4239119.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/6195125/pexels-photo-6195125.jpeg?auto=compress&cs=tinysrgb&w=800',
  ],
  painting: [
    'https://images.pexels.com/photos/1669754/pexels-photo-1669754.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/1023398/pexels-photo-1023398.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/6474471/pexels-photo-6474471.jpeg?auto=compress&cs=tinysrgb&w=800',
  ],
};

export const MOCK_PROVIDERS: ServiceProvider[] = [
  // Accra Providers
  {
    id: '1',
    name: 'Kwame Asante',
    email: '<EMAIL>',
    phone: '+233244123456',
    role: 'provider',
    professionalTitle: 'Master Plumber',
    experience: 8,
    serviceCategories: ['1'], // Plumbing
    serviceRadius: 15,
    rating: 4.8,
    reviewCount: 127,
    portfolioImages: portfolioImages.plumbing,
    isAvailable: true,
    isVerified: true,
    completedJobs: 245,
    responseTime: 15,
    createdAt: '2022-01-15T00:00:00Z',
    location: generateRandomLocation(GHANA_LOCATIONS[0]),
    workingHours: {
      monday: { isWorking: true, startTime: '08:00', endTime: '17:00' },
      tuesday: { isWorking: true, startTime: '08:00', endTime: '17:00' },
      wednesday: { isWorking: true, startTime: '08:00', endTime: '17:00' },
      thursday: { isWorking: true, startTime: '08:00', endTime: '17:00' },
      friday: { isWorking: true, startTime: '08:00', endTime: '17:00' },
      saturday: { isWorking: true, startTime: '09:00', endTime: '15:00' },
      sunday: { isWorking: false, startTime: '00:00', endTime: '00:00' },
    },
    pricing: [
      { subcategoryId: '1-1', basePrice: 80, currency: 'GHS', priceType: 'fixed' },
      { subcategoryId: '1-2', basePrice: 150, currency: 'GHS', priceType: 'fixed' },
      { subcategoryId: '1-3', basePrice: 100, currency: 'GHS', priceType: 'fixed' },
    ],
    profileImage: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=400',
  },
  {
    id: '2',
    name: 'Akosua Mensah',
    email: '<EMAIL>',
    phone: '+233244234567',
    role: 'provider',
    professionalTitle: 'Licensed Electrician',
    experience: 12,
    serviceCategories: ['2'], // Electrical
    serviceRadius: 20,
    rating: 4.9,
    reviewCount: 89,
    portfolioImages: portfolioImages.electrical,
    isAvailable: true,
    isVerified: true,
    completedJobs: 156,
    responseTime: 12,
    createdAt: '2021-08-20T00:00:00Z',
    location: generateRandomLocation(GHANA_LOCATIONS[0]),
    workingHours: {
      monday: { isWorking: true, startTime: '07:00', endTime: '18:00' },
      tuesday: { isWorking: true, startTime: '07:00', endTime: '18:00' },
      wednesday: { isWorking: true, startTime: '07:00', endTime: '18:00' },
      thursday: { isWorking: true, startTime: '07:00', endTime: '18:00' },
      friday: { isWorking: true, startTime: '07:00', endTime: '18:00' },
      saturday: { isWorking: true, startTime: '08:00', endTime: '16:00' },
      sunday: { isWorking: false, startTime: '00:00', endTime: '00:00' },
    },
    pricing: [
      { subcategoryId: '2-1', basePrice: 200, currency: 'GHS', priceType: 'hourly' },
      { subcategoryId: '2-2', basePrice: 90, currency: 'GHS', priceType: 'fixed' },
      { subcategoryId: '2-3', basePrice: 120, currency: 'GHS', priceType: 'fixed' },
    ],
    profileImage: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400',
  },
  {
    id: '3',
    name: 'Kofi Boateng',
    email: '<EMAIL>',
    phone: '+233244345678',
    role: 'provider',
    professionalTitle: 'Professional Carpenter',
    experience: 15,
    serviceCategories: ['3'], // Carpentry
    serviceRadius: 25,
    rating: 4.7,
    reviewCount: 203,
    portfolioImages: portfolioImages.carpentry,
    isAvailable: false,
    isVerified: true,
    completedJobs: 378,
    responseTime: 20,
    createdAt: '2020-05-10T00:00:00Z',
    location: generateRandomLocation(GHANA_LOCATIONS[0]),
    workingHours: {
      monday: { isWorking: true, startTime: '06:00', endTime: '17:00' },
      tuesday: { isWorking: true, startTime: '06:00', endTime: '17:00' },
      wednesday: { isWorking: true, startTime: '06:00', endTime: '17:00' },
      thursday: { isWorking: true, startTime: '06:00', endTime: '17:00' },
      friday: { isWorking: true, startTime: '06:00', endTime: '17:00' },
      saturday: { isWorking: true, startTime: '07:00', endTime: '14:00' },
      sunday: { isWorking: false, startTime: '00:00', endTime: '00:00' },
    },
    pricing: [
      { subcategoryId: '3-1', basePrice: 120, currency: 'GHS', priceType: 'hourly' },
      { subcategoryId: '3-2', basePrice: 500, currency: 'GHS', priceType: 'quote' },
      { subcategoryId: '3-3', basePrice: 180, currency: 'GHS', priceType: 'fixed' },
    ],
    profileImage: 'https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg?auto=compress&cs=tinysrgb&w=400',
  },
  {
    id: '4',
    name: 'Ama Osei',
    email: '<EMAIL>',
    phone: '+233244456789',
    role: 'provider',
    professionalTitle: 'Professional Cleaner',
    experience: 6,
    serviceCategories: ['4'], // Cleaning
    serviceRadius: 12,
    rating: 4.6,
    reviewCount: 78,
    portfolioImages: portfolioImages.cleaning,
    isAvailable: true,
    isVerified: true,
    completedJobs: 189,
    responseTime: 18,
    createdAt: '2022-03-01T00:00:00Z',
    location: generateRandomLocation(GHANA_LOCATIONS[0]),
    workingHours: {
      monday: { isWorking: true, startTime: '08:00', endTime: '16:00' },
      tuesday: { isWorking: true, startTime: '08:00', endTime: '16:00' },
      wednesday: { isWorking: true, startTime: '08:00', endTime: '16:00' },
      thursday: { isWorking: true, startTime: '08:00', endTime: '16:00' },
      friday: { isWorking: true, startTime: '08:00', endTime: '16:00' },
      saturday: { isWorking: true, startTime: '09:00', endTime: '15:00' },
      sunday: { isWorking: true, startTime: '10:00', endTime: '14:00' },
    },
    pricing: [
      { subcategoryId: '4-1', basePrice: 120, currency: 'GHS', priceType: 'fixed' },
      { subcategoryId: '4-2', basePrice: 150, currency: 'GHS', priceType: 'fixed' },
      { subcategoryId: '4-3', basePrice: 80, currency: 'GHS', priceType: 'fixed' },
    ],
    profileImage: 'https://images.pexels.com/photos/1181424/pexels-photo-1181424.jpeg?auto=compress&cs=tinysrgb&w=400',
  },
  {
    id: '5',
    name: 'Yaw Adjei',
    email: '<EMAIL>',
    phone: '+233244567890',
    role: 'provider',
    professionalTitle: 'Master Painter',
    experience: 10,
    serviceCategories: ['5'], // Painting
    serviceRadius: 18,
    rating: 4.8,
    reviewCount: 134,
    portfolioImages: portfolioImages.painting,
    isAvailable: true,
    isVerified: true,
    completedJobs: 267,
    responseTime: 25,
    createdAt: '2021-11-12T00:00:00Z',
    location: generateRandomLocation(GHANA_LOCATIONS[0]),
    workingHours: {
      monday: { isWorking: true, startTime: '07:00', endTime: '17:00' },
      tuesday: { isWorking: true, startTime: '07:00', endTime: '17:00' },
      wednesday: { isWorking: true, startTime: '07:00', endTime: '17:00' },
      thursday: { isWorking: true, startTime: '07:00', endTime: '17:00' },
      friday: { isWorking: true, startTime: '07:00', endTime: '17:00' },
      saturday: { isWorking: true, startTime: '08:00', endTime: '15:00' },
      sunday: { isWorking: false, startTime: '00:00', endTime: '00:00' },
    },
    pricing: [
      { subcategoryId: '5-1', basePrice: 180, currency: 'GHS', priceType: 'hourly' },
      { subcategoryId: '5-2', basePrice: 250, currency: 'GHS', priceType: 'hourly' },
      { subcategoryId: '5-3', basePrice: 100, currency: 'GHS', priceType: 'fixed' },
    ],
    profileImage: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=400',
  },
  // Kumasi Providers
  {
    id: '6',
    name: 'Nana Opreko',
    email: '<EMAIL>',
    phone: '+233244678901',
    role: 'provider',
    professionalTitle: 'Senior Plumber',
    experience: 14,
    serviceCategories: ['1'], // Plumbing
    serviceRadius: 20,
    rating: 4.9,
    reviewCount: 156,
    portfolioImages: portfolioImages.plumbing,
    isAvailable: true,
    isVerified: true,
    completedJobs: 298,
    responseTime: 10,
    createdAt: '2020-09-15T00:00:00Z',
    location: generateRandomLocation(GHANA_LOCATIONS[1]),
    workingHours: {
      monday: { isWorking: true, startTime: '08:00', endTime: '17:00' },
      tuesday: { isWorking: true, startTime: '08:00', endTime: '17:00' },
      wednesday: { isWorking: true, startTime: '08:00', endTime: '17:00' },
      thursday: { isWorking: true, startTime: '08:00', endTime: '17:00' },
      friday: { isWorking: true, startTime: '08:00', endTime: '17:00' },
      saturday: { isWorking: true, startTime: '09:00', endTime: '15:00' },
      sunday: { isWorking: false, startTime: '00:00', endTime: '00:00' },
    },
    pricing: [
      { subcategoryId: '1-1', basePrice: 75, currency: 'GHS', priceType: 'fixed' },
      { subcategoryId: '1-2', basePrice: 140, currency: 'GHS', priceType: 'fixed' },
      { subcategoryId: '1-3', basePrice: 95, currency: 'GHS', priceType: 'fixed' },
    ],
    profileImage: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400',
  },
  // Add more providers for other cities and categories...
];

// Helper function to get providers by location
export const getProvidersByLocation = (city: string): ServiceProvider[] => {
  return MOCK_PROVIDERS.filter(provider => provider.location?.city === city);
};

// Helper function to get providers by category
export const getProvidersByCategory = (categoryId: string): ServiceProvider[] => {
  return MOCK_PROVIDERS.filter(provider => 
    provider.serviceCategories.includes(categoryId)
  );
};

// Helper function to get featured providers (high rating and many reviews)
export const getFeaturedProviders = (): ServiceProvider[] => {
  return MOCK_PROVIDERS
    .filter(provider => provider.rating >= 4.7 && provider.reviewCount > 100)
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 6);
};