import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { RootState } from '../index';
import { User, ServiceProvider, ServiceRequest, Booking, Review, PaymentMethod, Transaction } from '@/types';

// Mock API base query that simulates network delays
const mockBaseQuery = fetchBaseQuery({
  baseUrl: '/api/v1',
  prepareHeaders: (headers, { getState }) => {
    const token = (getState() as RootState).auth.token;
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

// Simulate network delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: async (...args) => {
    await delay(500 + Math.random() * 1000); // 500-1500ms delay
    return mockBaseQuery(...args);
  },
  tagTypes: ['User', 'ServiceProvider', 'Booking', 'Review', 'PaymentMethod'],
  endpoints: (builder) => ({
    // Authentication endpoints
    sendOTP: builder.mutation<{ success: boolean }, { phone: string; countryCode: string }>({
      query: (credentials) => ({
        url: '/auth/send-otp',
        method: 'POST',
        body: credentials,
      }),
      // Mock response
      async queryFn() {
        await delay(1000);
        return { data: { success: true } };
      },
    }),
    
    verifyOTP: builder.mutation<{ user: User; token: string; refreshToken: string }, { phone: string; countryCode: string; otp: string }>({
      query: (data) => ({
        url: '/auth/verify-otp',
        method: 'POST',
        body: data,
      }),
      // Mock response
      async queryFn(arg) {
        await delay(1000);
        const mockUser: User = {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: arg.phone,
          role: 'customer',
          isVerified: true,
          createdAt: new Date().toISOString(),
        };
        return {
          data: {
            user: mockUser,
            token: 'mock-jwt-token',
            refreshToken: 'mock-refresh-token',
          },
        };
      },
    }),

    // Service Provider endpoints
    getServiceProviders: builder.query<ServiceProvider[], { 
      location?: [number, number];
      category?: string;
      radius?: number;
    }>({
      query: (params) => ({
        url: '/providers',
        params,
      }),
      providesTags: ['ServiceProvider'],
      // Mock response will be handled by mock data
    }),

    getServiceProvider: builder.query<ServiceProvider, string>({
      query: (id) => `/providers/${id}`,
      providesTags: (result, error, id) => [{ type: 'ServiceProvider', id }],
    }),

    // Booking endpoints
    createBooking: builder.mutation<Booking, Partial<ServiceRequest>>({
      query: (bookingData) => ({
        url: '/bookings',
        method: 'POST',
        body: bookingData,
      }),
      invalidatesTags: ['Booking'],
    }),

    getBookings: builder.query<Booking[], { status?: string; role?: 'customer' | 'provider' }>({
      query: (params) => ({
        url: '/bookings',
        params,
      }),
      providesTags: ['Booking'],
    }),

    updateBooking: builder.mutation<Booking, { id: string; updates: Partial<Booking> }>({
      query: ({ id, updates }) => ({
        url: `/bookings/${id}`,
        method: 'PATCH',
        body: updates,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Booking', id }],
    }),

    // Review endpoints
    createReview: builder.mutation<Review, Partial<Review>>({
      query: (reviewData) => ({
        url: '/reviews',
        method: 'POST',
        body: reviewData,
      }),
      invalidatesTags: ['Review', 'ServiceProvider'],
    }),

    getReviews: builder.query<Review[], { targetId: string; targetType: 'provider' | 'customer' }>({
      query: (params) => ({
        url: '/reviews',
        params,
      }),
      providesTags: ['Review'],
    }),

    // Payment endpoints
    getPaymentMethods: builder.query<PaymentMethod[], void>({
      query: () => '/payment-methods',
      providesTags: ['PaymentMethod'],
    }),

    addPaymentMethod: builder.mutation<PaymentMethod, Partial<PaymentMethod>>({
      query: (paymentMethod) => ({
        url: '/payment-methods',
        method: 'POST',
        body: paymentMethod,
      }),
      invalidatesTags: ['PaymentMethod'],
    }),

    processPayment: builder.mutation<Transaction, {
      bookingId: string;
      paymentMethodId: string;
      amount: number;
    }>({
      query: (paymentData) => ({
        url: '/payments/process',
        method: 'POST',
        body: paymentData,
      }),
    }),

    // User profile endpoints
    updateProfile: builder.mutation<User, Partial<User>>({
      query: (updates) => ({
        url: '/user/profile',
        method: 'PATCH',
        body: updates,
      }),
      invalidatesTags: ['User'],
    }),
  }),
});

export const {
  useSendOTPMutation,
  useVerifyOTPMutation,
  useGetServiceProvidersQuery,
  useGetServiceProviderQuery,
  useCreateBookingMutation,
  useGetBookingsQuery,
  useUpdateBookingMutation,
  useCreateReviewMutation,
  useGetReviewsQuery,
  useGetPaymentMethodsQuery,
  useAddPaymentMethodMutation,
  useProcessPaymentMutation,
  useUpdateProfileMutation,
} = apiSlice;