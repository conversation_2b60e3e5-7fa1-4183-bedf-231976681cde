import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useSelector } from 'react-redux';
import { MapPin, Bell } from 'lucide-react-native';
import { RootState } from '../../store';
import { ServiceCard } from '../../components/molecules/ServiceCard';
import { CategoryCard } from '../../components/molecules/CategoryCard';
import { mockCategories, mockProviders } from '../../data/mockData';
import { theme } from '../../theme/colors';

export default function HomeScreen() {
  const { user } = useSelector((state: RootState) => state.auth);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <View style={styles.locationContainer}>
              <MapPin size={16} color={theme.colors.gray[600]} />
              <Text style={styles.locationText}>Accra, Ghana</Text>
            </View>
            <Text style={styles.greeting}>
              Good morning, {user?.name || 'Welcome'}!
            </Text>
          </View>
          <TouchableOpacity style={styles.notificationButton}>
            <Bell size={24} color={theme.colors.gray[700]} />
          </TouchableOpacity>
        </View>

        {/* Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Popular Services</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          >
            {mockCategories.map((category) => (
              <CategoryCard
                key={category.id}
                category={category}
                onPress={() => console.log('Category pressed:', category.name)}
              />
            ))}
          </ScrollView>
        </View>

        {/* Featured Providers */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Providers</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See all</Text>
            </TouchableOpacity>
          </View>
          {mockProviders.map((provider) => (
            <ServiceCard
              key={provider.id}
              provider={provider}
              onPress={() => console.log('Provider pressed:', provider.user.name)}
            />
          ))}
        </View>

        {/* Recent Services */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Book Again</Text>
          <View style={styles.recentCard}>
            <Text style={styles.recentTitle}>Kwame Asante</Text>
            <Text style={styles.recentService}>Plumbing Service</Text>
            <TouchableOpacity style={styles.bookAgainButton}>
              <Text style={styles.bookAgainText}>Book Again</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.gray[50],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
  },
  headerLeft: {
    flex: 1,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  locationText: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[600],
    marginLeft: theme.spacing.xs,
  },
  greeting: {
    fontFamily: 'Inter-SemiBold',
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.gray[900],
  },
  notificationButton: {
    padding: theme.spacing.sm,
  },
  section: {
    marginTop: theme.spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.gray[900],
    paddingHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.md,
  },
  seeAllText: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.primary[600],
  },
  categoriesContainer: {
    paddingHorizontal: theme.spacing.lg,
  },
  recentCard: {
    backgroundColor: theme.colors.white,
    margin: theme.spacing.lg,
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.sm,
  },
  recentTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.xs,
  },
  recentService: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[600],
    marginBottom: theme.spacing.md,
  },
  bookAgainButton: {
    backgroundColor: theme.colors.primary[600],
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    alignSelf: 'flex-start',
  },
  bookAgainText: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.white,
  },
});