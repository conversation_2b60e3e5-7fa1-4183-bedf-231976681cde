import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { colors, spacing, typography } from '@/constants/theme';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import { TouchableOpacity } from 'react-native';

export default function PhoneScreen() {
  const [phone, setPhone] = useState('');
  const [countryCode, setCountryCode] = useState('+233');
  const [isLoading, setIsLoading] = useState(false);

  const handleSendOTP = async () => {
    if (!phone || phone.length < 9) {
      Alert.alert('Error', 'Please enter a valid phone number');
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      router.push({
        pathname: '/(auth)/otp',
        params: { phone, countryCode }
      });
    }, 1500);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft color={colors.gray[600]} size={24} />
        </TouchableOpacity>
        <Text style={styles.title}>Enter your phone number</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.description}>
          We'll send you a verification code to confirm your number
        </Text>

        <View style={styles.phoneInputContainer}>
          <View style={styles.countryCodeContainer}>
            <Text style={styles.countryCode}>{countryCode}</Text>
          </View>
          <Input
            placeholder="24 123 4567"
            value={phone}
            onChangeText={setPhone}
            keyboardType="phone-pad"
            autoCapitalize="none"
          />
        </View>

        <Button
          variant="primary"
          size="lg"
          onPress={handleSendOTP}
          loading={isLoading}
          disabled={!phone}
          fullWidth
        >
          Send Verification Code
        </Button>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    gap: spacing.md,
  },
  backButton: {
    padding: spacing.sm,
  },
  title: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing['2xl'],
  },
  description: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
    marginBottom: spacing['3xl'],
    lineHeight: 24,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: spacing.md,
    marginBottom: spacing['3xl'],
  },
  countryCodeContainer: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md + 2,
    minWidth: 80,
    alignItems: 'center',
  },
  countryCode: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Medium',
    color: colors.gray[900],
  },
});