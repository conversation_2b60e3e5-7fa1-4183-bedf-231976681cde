import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  Image,
  FlatList,
  Dimensions,
  RefreshControl
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Bell, MapPin, Search, Star, ChevronRight } from 'lucide-react-native';
import { colors, spacing, typography, borderRadius } from '@/constants/theme';
import { SERVICE_CATEGORIES } from '@/constants/services';
import { MOCK_PROVIDERS, getFeaturedProviders } from '@/data/mockProviders';
import { useAppSelector } from '@/store/hooks';
import Button from '@/components/common/Button';

const { width } = Dimensions.get('window');

export default function HomeScreen() {
  const { user } = useAppSelector(state => state.auth);
  const [featuredProviders, setFeaturedProviders] = useState(getFeaturedProviders());
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = () => {
    setRefreshing(true);
    // Simulate API refresh
    setTimeout(() => {
      setFeaturedProviders(getFeaturedProviders());
      setRefreshing(false);
    }, 1000);
  };

  const renderServiceCategory = ({ item }: { item: typeof SERVICE_CATEGORIES[0] }) => (
    <TouchableOpacity 
      style={styles.categoryCard}
      onPress={() => router.push({
        pathname: '/(tabs)/search',
        params: { category: item.id }
      })}
    >
      <View style={styles.categoryIcon}>
        <Text style={styles.categoryIconText}>{item.name.slice(0, 2)}</Text>
      </View>
      <Text style={styles.categoryName}>{item.name}</Text>
      <Text style={styles.categoryPrice}>
        From GHS {item.averagePrice.min}
      </Text>
    </TouchableOpacity>
  );

  const renderFeaturedProvider = ({ item }: { item: typeof featuredProviders[0] }) => (
    <TouchableOpacity 
      style={styles.providerCard}
      onPress={() => router.push(`/provider/${item.id}`)}
    >
      <Image source={{ uri: item.profileImage }} style={styles.providerImage} />
      <View style={styles.providerInfo}>
        <Text style={styles.providerName}>{item.name}</Text>
        <Text style={styles.providerTitle}>{item.professionalTitle}</Text>
        <View style={styles.providerRating}>
          <Star size={14} color={colors.warning[500]} fill={colors.warning[500]} />
          <Text style={styles.ratingText}>{item.rating}</Text>
          <Text style={styles.reviewCount}>({item.reviewCount})</Text>
        </View>
        <Text style={styles.providerLocation}>{item.location?.city}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Text style={styles.greeting}>Hello, {user?.name || 'User'}!</Text>
            <View style={styles.locationContainer}>
              <MapPin size={16} color={colors.gray[500]} />
              <Text style={styles.location}>Accra, Ghana</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.notificationButton}>
            <Bell size={24} color={colors.gray[700]} />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <TouchableOpacity 
          style={styles.searchBar}
          onPress={() => router.push('/(tabs)/search')}
        >
          <Search size={20} color={colors.gray[400]} />
          <Text style={styles.searchPlaceholder}>What service do you need?</Text>
        </TouchableOpacity>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity style={styles.quickAction}>
            <Text style={styles.quickActionEmoji}>🔧</Text>
            <Text style={styles.quickActionText}>Emergency</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.quickAction}>
            <Text style={styles.quickActionEmoji}>📅</Text>
            <Text style={styles.quickActionText}>Schedule</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.quickAction}>
            <Text style={styles.quickActionEmoji}>🏠</Text>
            <Text style={styles.quickActionText}>Home Services</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.quickAction}>
            <Text style={styles.quickActionEmoji}>⭐</Text>
            <Text style={styles.quickActionText}>Top Rated</Text>
          </TouchableOpacity>
        </View>

        {/* Service Categories */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Popular Services</Text>
            <TouchableOpacity onPress={() => router.push('/(tabs)/search')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <FlatList
            data={SERVICE_CATEGORIES.slice(0, 6)}
            renderItem={renderServiceCategory}
            keyExtractor={(item) => item.id}
            numColumns={2}
            scrollEnabled={false}
            contentContainerStyle={styles.categoriesGrid}
          />
        </View>

        {/* Featured Providers */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Top Rated Providers</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <FlatList
            data={featuredProviders}
            renderItem={renderFeaturedProvider}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.providersContainer}
          />
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          <View style={styles.activityCard}>
            <View style={styles.activityIcon}>
              <Text style={styles.activityEmoji}>🔧</Text>
            </View>
            <View style={styles.activityInfo}>
              <Text style={styles.activityTitle}>Plumbing Service Completed</Text>
              <Text style={styles.activitySubtitle}>Kwame Asante • 2 days ago</Text>
            </View>
            <TouchableOpacity>
              <ChevronRight size={20} color={colors.gray[400]} />
            </TouchableOpacity>
          </View>
        </View>

        {/* CTA Section */}
        <View style={styles.ctaSection}>
          <Text style={styles.ctaTitle}>Need a service provider?</Text>
          <Text style={styles.ctaSubtitle}>
            Book trusted professionals in your area
          </Text>
          <Button
            variant="primary"
            size="lg"
            onPress={() => router.push('/(tabs)/search')}
            fullWidth
          >
            Find Services
          </Button>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
  },
  headerLeft: {
    flex: 1,
  },
  greeting: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  location: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  notificationButton: {
    padding: spacing.sm,
    borderRadius: borderRadius.md,
    backgroundColor: colors.gray[50],
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
    marginHorizontal: spacing.xl,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.lg,
    gap: spacing.md,
    marginBottom: spacing.xl,
  },
  searchPlaceholder: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[400],
    flex: 1,
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: spacing.xl,
    marginBottom: spacing.xl,
    gap: spacing.md,
  },
  quickAction: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: colors.gray[50],
    paddingVertical: spacing.lg,
    borderRadius: borderRadius.lg,
  },
  quickActionEmoji: {
    fontSize: 24,
    marginBottom: spacing.xs,
  },
  quickActionText: {
    fontSize: typography.fontSize.xs,
    fontFamily: 'Inter-Medium',
    color: colors.gray[700],
    textAlign: 'center',
  },
  section: {
    marginBottom: spacing['2xl'],
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  seeAllText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Medium',
    color: colors.primary[600],
  },
  categoriesGrid: {
    paddingHorizontal: spacing.xl,
    gap: spacing.md,
  },
  categoryCard: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    marginHorizontal: spacing.xs,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.gray[200],
    alignItems: 'center',
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  categoryIconText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.primary[600],
  },
  categoryName: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  categoryPrice: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
    textAlign: 'center',
  },
  providersContainer: {
    paddingHorizontal: spacing.xl,
    gap: spacing.lg,
  },
  providerCard: {
    width: 160,
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  providerImage: {
    width: '100%',
    height: 100,
    borderRadius: borderRadius.md,
    marginBottom: spacing.md,
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  providerTitle: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
    marginBottom: spacing.xs,
  },
  providerRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    marginBottom: spacing.xs,
  },
  ratingText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  reviewCount: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  providerLocation: {
    fontSize: typography.fontSize.xs,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  activityCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    marginHorizontal: spacing.xl,
    borderWidth: 1,
    borderColor: colors.gray[200],
    gap: spacing.md,
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.success[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  activityEmoji: {
    fontSize: 20,
  },
  activityInfo: {
    flex: 1,
  },
  activityTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  activitySubtitle: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  ctaSection: {
    backgroundColor: colors.primary[50],
    marginHorizontal: spacing.xl,
    borderRadius: borderRadius.lg,
    padding: spacing['2xl'],
    alignItems: 'center',
    marginBottom: spacing['3xl'],
  },
  ctaTitle: {
    fontSize: typography.fontSize.xl,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  ctaSubtitle: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
    marginBottom: spacing.xl,
    textAlign: 'center',
  },
});