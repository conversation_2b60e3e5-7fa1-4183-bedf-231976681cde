import { ServiceCategory } from '@/types';

export const SERVICE_CATEGORIES: ServiceCategory[] = [
  {
    id: '1',
    name: 'Plumbing',
    icon: 'wrench',
    description: 'Professional plumbing services for homes and businesses',
    popularityRank: 1,
    averagePrice: { min: 50, max: 200, currency: 'GHS' },
    subcategories: [
      {
        id: '1-1',
        name: 'Pipe Repair',
        description: 'Fix leaking or damaged pipes',
        estimatedDuration: 120,
        priceRange: { min: 50, max: 150, currency: 'GHS' },
      },
      {
        id: '1-2',
        name: 'Toilet Installation',
        description: 'Install new toilet or replace existing one',
        estimatedDuration: 180,
        priceRange: { min: 100, max: 200, currency: 'GHS' },
      },
      {
        id: '1-3',
        name: 'Drainage Cleaning',
        description: 'Clear blocked drains and pipes',
        estimatedDuration: 90,
        priceRange: { min: 80, max: 120, currency: 'GHS' },
      },
      {
        id: '1-4',
        name: 'Emergency Plumbing',
        description: '24/7 emergency plumbing services',
        estimatedDuration: 60,
        priceRange: { min: 150, max: 300, currency: 'GHS' },
      },
    ],
  },
  {
    id: '2',
    name: 'Electrical',
    icon: 'zap',
    description: 'Licensed electrical services and installations',
    popularityRank: 2,
    averagePrice: { min: 75, max: 300, currency: 'GHS' },
    subcategories: [
      {
        id: '2-1',
        name: 'Wiring Installation',
        description: 'Install new electrical wiring',
        estimatedDuration: 240,
        priceRange: { min: 150, max: 300, currency: 'GHS' },
      },
      {
        id: '2-2',
        name: 'Socket Repair',
        description: 'Fix faulty electrical sockets',
        estimatedDuration: 60,
        priceRange: { min: 75, max: 120, currency: 'GHS' },
      },
      {
        id: '2-3',
        name: 'Light Installation',
        description: 'Install ceiling fans, lights, and fixtures',
        estimatedDuration: 90,
        priceRange: { min: 100, max: 200, currency: 'GHS' },
      },
      {
        id: '2-4',
        name: 'Electrical Maintenance',
        description: 'General electrical system maintenance',
        estimatedDuration: 120,
        priceRange: { min: 120, max: 180, currency: 'GHS' },
      },
    ],
  },
  {
    id: '3',
    name: 'Carpentry',
    icon: 'hammer',
    description: 'Custom woodwork and furniture services',
    popularityRank: 3,
    averagePrice: { min: 100, max: 500, currency: 'GHS' },
    subcategories: [
      {
        id: '3-1',
        name: 'Furniture Repair',
        description: 'Repair damaged furniture',
        estimatedDuration: 180,
        priceRange: { min: 100, max: 250, currency: 'GHS' },
      },
      {
        id: '3-2',
        name: 'Custom Furniture',
        description: 'Build custom furniture pieces',
        estimatedDuration: 480,
        priceRange: { min: 300, max: 800, currency: 'GHS' },
      },
      {
        id: '3-3',
        name: 'Door Installation',
        description: 'Install or repair doors',
        estimatedDuration: 120,
        priceRange: { min: 150, max: 300, currency: 'GHS' },
      },
      {
        id: '3-4',
        name: 'Shelving',
        description: 'Install shelves and storage solutions',
        estimatedDuration: 90,
        priceRange: { min: 80, max: 200, currency: 'GHS' },
      },
    ],
  },
  {
    id: '4',
    name: 'Cleaning',
    icon: 'sparkles',
    description: 'Professional cleaning services for homes and offices',
    popularityRank: 4,
    averagePrice: { min: 30, max: 150, currency: 'GHS' },
    subcategories: [
      {
        id: '4-1',
        name: 'House Cleaning',
        description: 'Deep cleaning for residential homes',
        estimatedDuration: 180,
        priceRange: { min: 80, max: 150, currency: 'GHS' },
      },
      {
        id: '4-2',
        name: 'Office Cleaning',
        description: 'Commercial cleaning services',
        estimatedDuration: 240,
        priceRange: { min: 100, max: 200, currency: 'GHS' },
      },
      {
        id: '4-3',
        name: 'Carpet Cleaning',
        description: 'Professional carpet cleaning',
        estimatedDuration: 120,
        priceRange: { min: 60, max: 120, currency: 'GHS' },
      },
      {
        id: '4-4',
        name: 'Window Cleaning',
        description: 'Interior and exterior window cleaning',
        estimatedDuration: 90,
        priceRange: { min: 40, max: 80, currency: 'GHS' },
      },
    ],
  },
  {
    id: '5',
    name: 'Painting',
    icon: 'palette',
    description: 'Interior and exterior painting services',
    popularityRank: 5,
    averagePrice: { min: 80, max: 400, currency: 'GHS' },
    subcategories: [
      {
        id: '5-1',
        name: 'Interior Painting',
        description: 'Paint interior walls and ceilings',
        estimatedDuration: 360,
        priceRange: { min: 150, max: 400, currency: 'GHS' },
      },
      {
        id: '5-2',
        name: 'Exterior Painting',
        description: 'Paint exterior walls and surfaces',
        estimatedDuration: 480,
        priceRange: { min: 200, max: 500, currency: 'GHS' },
      },
      {
        id: '5-3',
        name: 'Touch-up Painting',
        description: 'Small paint touch-ups and repairs',
        estimatedDuration: 120,
        priceRange: { min: 80, max: 150, currency: 'GHS' },
      },
    ],
  },
  {
    id: '6',
    name: 'Appliance Repair',
    icon: 'settings',
    description: 'Repair and maintenance of home appliances',
    popularityRank: 6,
    averagePrice: { min: 60, max: 250, currency: 'GHS' },
    subcategories: [
      {
        id: '6-1',
        name: 'Refrigerator Repair',
        description: 'Fix refrigerator and freezer issues',
        estimatedDuration: 120,
        priceRange: { min: 100, max: 200, currency: 'GHS' },
      },
      {
        id: '6-2',
        name: 'Washing Machine Repair',
        description: 'Repair washing machines',
        estimatedDuration: 90,
        priceRange: { min: 80, max: 150, currency: 'GHS' },
      },
      {
        id: '6-3',
        name: 'AC Repair',
        description: 'Air conditioning repair and maintenance',
        estimatedDuration: 120,
        priceRange: { min: 120, max: 250, currency: 'GHS' },
      },
    ],
  },
  {
    id: '7',
    name: 'Gardening',
    icon: 'leaf',
    description: 'Garden maintenance and landscaping services',
    popularityRank: 7,
    averagePrice: { min: 40, max: 200, currency: 'GHS' },
    subcategories: [
      {
        id: '7-1',
        name: 'Lawn Mowing',
        description: 'Regular lawn cutting and maintenance',
        estimatedDuration: 60,
        priceRange: { min: 40, max: 80, currency: 'GHS' },
      },
      {
        id: '7-2',
        name: 'Garden Design',
        description: 'Landscape design and installation',
        estimatedDuration: 480,
        priceRange: { min: 200, max: 500, currency: 'GHS' },
      },
      {
        id: '7-3',
        name: 'Plant Care',
        description: 'Plant maintenance and care services',
        estimatedDuration: 90,
        priceRange: { min: 50, max: 100, currency: 'GHS' },
      },
    ],
  },
  {
    id: '8',
    name: 'Locksmith',
    icon: 'key',
    description: 'Professional locksmith services',
    popularityRank: 8,
    averagePrice: { min: 50, max: 200, currency: 'GHS' },
    subcategories: [
      {
        id: '8-1',
        name: 'Lock Installation',
        description: 'Install new locks and security systems',
        estimatedDuration: 60,
        priceRange: { min: 80, max: 150, currency: 'GHS' },
      },
      {
        id: '8-2',
        name: 'Emergency Lockout',
        description: '24/7 emergency lockout services',
        estimatedDuration: 30,
        priceRange: { min: 100, max: 200, currency: 'GHS' },
      },
      {
        id: '8-3',
        name: 'Key Duplication',
        description: 'Duplicate keys and remotes',
        estimatedDuration: 15,
        priceRange: { min: 20, max: 50, currency: 'GHS' },
      },
    ],
  },
];

// Ghana-specific locations
export const GHANA_LOCATIONS = [
  {
    id: '1',
    city: 'Accra',
    region: 'Greater Accra',
    coordinates: [-0.1969, 5.6037] as [number, number],
    neighborhoods: [
      'East Legon',
      'Cantonments',
      'Airport Hills',
      'Labone',
      'Osu',
      'Adabraka',
      'Dansoman',
      'Tema',
      'Kasoa',
      'Madina',
    ],
  },
  {
    id: '2',
    city: 'Kumasi',
    region: 'Ashanti',
    coordinates: [-1.6163, 6.6885] as [number, number],
    neighborhoods: [
      'Asokwa',
      'Bantama',
      'Kwadaso',
      'Suame',
      'Adum',
      'Nhyiaeso',
      'Ayeduase',
      'Kotei',
    ],
  },
  {
    id: '3',
    city: 'Tamale',
    region: 'Northern',
    coordinates: [-0.8524, 9.4008] as [number, number],
    neighborhoods: [
      'Tamale Central',
      'Vittin',
      'Kakpayili',
      'Jisonayili',
      'Gulkpegu',
    ],
  },
  {
    id: '4',
    city: 'Cape Coast',
    region: 'Central',
    coordinates: [-1.2466, 5.1053] as [number, number],
    neighborhoods: [
      'Cape Coast Central',
      'Pedu',
      'Adisadel',
      'Amamoma',
      'Kwaprow',
    ],
  },
];