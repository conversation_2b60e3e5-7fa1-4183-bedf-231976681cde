import { ServiceCategory, ServiceProvider, Service, Booking } from '../types';

export const mockCategories: ServiceCategory[] = [
  {
    id: '1',
    name: 'Plumbing',
    icon: '🔧',
    color: '#3B82F6',
  },
  {
    id: '2',
    name: 'Electrical',
    icon: '⚡',
    color: '#F59E0B',
  },
  {
    id: '3',
    name: 'Carpentry',
    icon: '🔨',
    color: '#10B981',
  },
  {
    id: '4',
    name: 'Cleaning',
    icon: '🧹',
    color: '#8B5CF6',
  },
  {
    id: '5',
    name: 'Painting',
    icon: '🎨',
    color: '#F97316',
  },
  {
    id: '6',
    name: 'Gardening',
    icon: '🌱',
    color: '#059669',
  },
];

export const mockServices: Service[] = [
  {
    id: '1',
    name: 'Pipe Repair',
    description: 'Fix leaking pipes and faucets',
    category: mockCategories[0],
    basePrice: 50,
    duration: 60,
  },
  {
    id: '2',
    name: 'Electrical Wiring',
    description: 'Install and repair electrical systems',
    category: mockCategories[1],
    basePrice: 80,
    duration: 120,
  },
  {
    id: '3',
    name: 'Furniture Assembly',
    description: 'Assemble furniture and custom woodwork',
    category: mockCategories[2],
    basePrice: 40,
    duration: 90,
  },
  {
    id: '4',
    name: 'House Cleaning',
    description: 'Deep cleaning for homes and offices',
    category: mockCategories[3],
    basePrice: 30,
    duration: 120,
  },
  {
    id: '5',
    name: 'Interior Painting',
    description: 'Professional interior wall painting',
    category: mockCategories[4],
    basePrice: 60,
    duration: 240,
  },
];

export const mockProviders: ServiceProvider[] = [
  {
    id: '1',
    user: {
      id: '1',
      name: 'Kwame Asante',
      email: '<EMAIL>',
      phone: '+************',
      avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=400',
      role: 'provider',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    services: [mockServices[0]],
    location: {
      latitude: 5.6037,
      longitude: -0.1870,
      address: 'Accra, Ghana',
    },
    rating: 4.8,
    reviewCount: 124,
    availability: {},
    pricing: {
      min: 50,
      max: 150,
      currency: 'GHS',
    },
    portfolio: [],
    isVerified: true,
    isAvailable: true,
  },
  {
    id: '2',
    user: {
      id: '2',
      name: 'Ama Osei',
      email: '<EMAIL>',
      phone: '+233241234568',
      avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=400',
      role: 'provider',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    services: [mockServices[1]],
    location: {
      latitude: 5.6037,
      longitude: -0.1870,
      address: 'Tema, Ghana',
    },
    rating: 4.6,
    reviewCount: 89,
    availability: {},
    pricing: {
      min: 80,
      max: 200,
      currency: 'GHS',
    },
    portfolio: [],
    isVerified: true,
    isAvailable: false,
  },
  {
    id: '3',
    user: {
      id: '3',
      name: 'Kofi Mensah',
      email: '<EMAIL>',
      phone: '+233241234569',
      avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=400',
      role: 'provider',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    services: [mockServices[2]],
    location: {
      latitude: 5.6037,
      longitude: -0.1870,
      address: 'Kumasi, Ghana',
    },
    rating: 4.9,
    reviewCount: 156,
    availability: {},
    pricing: {
      min: 40,
      max: 120,
      currency: 'GHS',
    },
    portfolio: [],
    isVerified: true,
    isAvailable: true,
  },
  {
    id: '4',
    user: {
      id: '4',
      name: 'Akosua Adjei',
      email: '<EMAIL>',
      phone: '+233241234570',
      avatar: 'https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=400',
      role: 'provider',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    services: [mockServices[3]],
    location: {
      latitude: 5.6037,
      longitude: -0.1870,
      address: 'East Legon, Accra',
    },
    rating: 4.7,
    reviewCount: 203,
    availability: {},
    pricing: {
      min: 30,
      max: 80,
      currency: 'GHS',
    },
    portfolio: [],
    isVerified: true,
    isAvailable: true,
  },
];

export const mockBookings: Booking[] = [
  {
    id: '1',
    customer: {
      id: 'user1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+************',
      role: 'customer',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    provider: mockProviders[0],
    service: mockServices[0],
    status: 'confirmed',
    scheduledDate: '2024-12-28',
    scheduledTime: '14:00',
    location: {
      latitude: 5.6037,
      longitude: -0.1870,
      address: '123 Main Street, Accra, Ghana',
    },
    payment: {
      id: 'pay_1',
      provider: 'paystack',
      amount: 75,
      status: 'success',
      reference: 'ref_123456',
      method: 'card',
      createdAt: '2024-12-26T10:00:00Z',
    },
    notes: 'Please bring all necessary tools. Kitchen sink repair needed.',
    createdAt: '2024-12-26T10:00:00Z',
    updatedAt: '2024-12-26T10:00:00Z',
  },
  {
    id: '2',
    customer: {
      id: 'user1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+************',
      role: 'customer',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    provider: mockProviders[1],
    service: mockServices[1],
    status: 'in_progress',
    scheduledDate: '2024-12-27',
    scheduledTime: '09:00',
    location: {
      latitude: 5.6037,
      longitude: -0.1870,
      address: '456 Oak Avenue, Tema, Ghana',
    },
    payment: {
      id: 'pay_2',
      provider: 'paystack',
      amount: 120,
      status: 'success',
      reference: 'ref_789012',
      method: 'mobile_money',
      createdAt: '2024-12-25T15:30:00Z',
    },
    notes: 'Installing ceiling fan in living room.',
    createdAt: '2024-12-25T15:30:00Z',
    updatedAt: '2024-12-27T09:15:00Z',
  },
  {
    id: '3',
    customer: {
      id: 'user1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+************',
      role: 'customer',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    provider: mockProviders[2],
    service: mockServices[2],
    status: 'completed',
    scheduledDate: '2024-12-20',
    scheduledTime: '10:30',
    location: {
      latitude: 5.6037,
      longitude: -0.1870,
      address: '789 Pine Street, Kumasi, Ghana',
    },
    payment: {
      id: 'pay_3',
      provider: 'paystack',
      amount: 60,
      status: 'success',
      reference: 'ref_345678',
      method: 'card',
      createdAt: '2024-12-18T12:00:00Z',
    },
    notes: 'Assembled IKEA wardrobe and bedside table.',
    createdAt: '2024-12-18T12:00:00Z',
    updatedAt: '2024-12-20T14:30:00Z',
  },
  {
    id: '4',
    customer: {
      id: 'user1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+************',
      role: 'customer',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    provider: mockProviders[3],
    service: mockServices[3],
    status: 'pending',
    scheduledDate: '2024-12-30',
    scheduledTime: '08:00',
    location: {
      latitude: 5.6037,
      longitude: -0.1870,
      address: '321 Cedar Road, East Legon, Accra',
    },
    payment: {
      id: 'pay_4',
      provider: 'paystack',
      amount: 45,
      status: 'pending',
      reference: 'ref_901234',
      method: 'bank_transfer',
      createdAt: '2024-12-26T16:45:00Z',
    },
    notes: 'Deep cleaning for 3-bedroom apartment.',
    createdAt: '2024-12-26T16:45:00Z',
    updatedAt: '2024-12-26T16:45:00Z',
  },
  {
    id: '5',
    customer: {
      id: 'user1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+************',
      role: 'customer',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    provider: mockProviders[0],
    service: mockServices[4],
    status: 'cancelled',
    scheduledDate: '2024-12-15',
    scheduledTime: '13:00',
    location: {
      latitude: 5.6037,
      longitude: -0.1870,
      address: '654 Maple Drive, Accra, Ghana',
    },
    payment: {
      id: 'pay_5',
      provider: 'paystack',
      amount: 90,
      status: 'refunded',
      reference: 'ref_567890',
      method: 'card',
      createdAt: '2024-12-12T11:20:00Z',
    },
    notes: 'Painting living room walls - cancelled due to scheduling conflict.',
    createdAt: '2024-12-12T11:20:00Z',
    updatedAt: '2024-12-14T09:30:00Z',
  },
];