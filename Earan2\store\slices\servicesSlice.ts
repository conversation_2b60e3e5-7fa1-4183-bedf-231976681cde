import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ServiceProvider, ServiceCategory } from '@/types';

interface ServicesState {
  categories: ServiceCategory[];
  providers: ServiceProvider[];
  featuredProviders: ServiceProvider[];
  searchResults: ServiceProvider[];
  isLoading: boolean;
  error: string | null;
  filters: {
    category: string | null;
    priceRange: [number, number] | null;
    rating: number | null;
    distance: number | null;
    availability: 'any' | 'now' | 'today' | 'this_week';
  };
  sortBy: 'distance' | 'rating' | 'price' | 'popularity';
}

const initialState: ServicesState = {
  categories: [],
  providers: [],
  featuredProviders: [],
  searchResults: [],
  isLoading: false,
  error: null,
  filters: {
    category: null,
    priceRange: null,
    rating: null,
    distance: null,
    availability: 'any',
  },
  sortBy: 'distance',
};

const servicesSlice = createSlice({
  name: 'services',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setCategories: (state, action: PayloadAction<ServiceCategory[]>) => {
      state.categories = action.payload;
    },
    setProviders: (state, action: PayloadAction<ServiceProvider[]>) => {
      state.providers = action.payload;
    },
    setFeaturedProviders: (state, action: PayloadAction<ServiceProvider[]>) => {
      state.featuredProviders = action.payload;
    },
    setSearchResults: (state, action: PayloadAction<ServiceProvider[]>) => {
      state.searchResults = action.payload;
    },
    updateFilters: (state, action: PayloadAction<Partial<ServicesState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setSortBy: (state, action: PayloadAction<ServicesState['sortBy']>) => {
      state.sortBy = action.payload;
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    updateProviderAvailability: (state, action: PayloadAction<{ providerId: string; isAvailable: boolean }>) => {
      const provider = state.providers.find(p => p.id === action.payload.providerId);
      if (provider) {
        provider.isAvailable = action.payload.isAvailable;
      }
    },
  },
});

export const {
  setLoading,
  setError,
  setCategories,
  setProviders,
  setFeaturedProviders,
  setSearchResults,
  updateFilters,
  setSortBy,
  clearFilters,
  updateProviderAvailability,
} = servicesSlice.actions;

export default servicesSlice.reducer;