import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Calendar, Clock, MapPin, Phone, MessageSquare, Star } from 'lucide-react-native';
import { Booking, BookingStatus } from '../../types';
import { theme } from '../../theme/colors';
import { Button } from '../atoms/Button';

interface BookingCardProps {
  booking: Booking;
  onPress: () => void;
  onContact?: () => void;
  onMessage?: () => void;
}

export function BookingCard({ booking, onPress, onContact, onMessage }: BookingCardProps) {
  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
      case 'pending':
        return theme.colors.warning[500];
      case 'confirmed':
        return theme.colors.primary[600];
      case 'in_progress':
        return theme.colors.secondary[600];
      case 'completed':
        return theme.colors.success[600];
      case 'cancelled':
      case 'disputed':
        return theme.colors.error[500];
      default:
        return theme.colors.gray[500];
    }
  };

  const getStatusText = (status: BookingStatus) => {
    switch (status) {
      case 'pending':
        return 'Pending Confirmation';
      case 'confirmed':
        return 'Confirmed';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'disputed':
        return 'Disputed';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      {/* Status Badge */}
      <View style={[styles.statusBadge, { backgroundColor: getStatusColor(booking.status) }]}>
        <Text style={styles.statusText}>{getStatusText(booking.status)}</Text>
      </View>

      {/* Provider Info */}
      <View style={styles.providerSection}>
        <Image
          source={{
            uri: booking.provider.user.avatar || 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=400',
          }}
          style={styles.providerAvatar}
        />
        <View style={styles.providerInfo}>
          <Text style={styles.providerName}>{booking.provider.user.name}</Text>
          <Text style={styles.serviceName}>{booking.service.name}</Text>
          <View style={styles.rating}>
            <Star size={14} color={theme.colors.accent[500]} fill={theme.colors.accent[500]} />
            <Text style={styles.ratingText}>{booking.provider.rating.toFixed(1)}</Text>
          </View>
        </View>
      </View>

      {/* Booking Details */}
      <View style={styles.detailsSection}>
        <View style={styles.detailRow}>
          <Calendar size={16} color={theme.colors.gray[500]} />
          <Text style={styles.detailText}>{formatDate(booking.scheduledDate)}</Text>
        </View>
        <View style={styles.detailRow}>
          <Clock size={16} color={theme.colors.gray[500]} />
          <Text style={styles.detailText}>{formatTime(booking.scheduledTime)}</Text>
        </View>
        <View style={styles.detailRow}>
          <MapPin size={16} color={theme.colors.gray[500]} />
          <Text style={styles.detailText} numberOfLines={1}>
            {booking.location.address || 'Location provided'}
          </Text>
        </View>
      </View>

      {/* Payment Info */}
      {booking.payment && (
        <View style={styles.paymentSection}>
          <Text style={styles.paymentAmount}>₵{booking.payment.amount}</Text>
          <Text style={[
            styles.paymentStatus,
            { color: booking.payment.status === 'success' ? theme.colors.success[600] : theme.colors.warning[600] }
          ]}>
            {booking.payment.status === 'success' ? 'Paid' : 'Pending Payment'}
          </Text>
        </View>
      )}

      {/* Action Buttons */}
      {(booking.status === 'confirmed' || booking.status === 'in_progress') && (
        <View style={styles.actionsSection}>
          <TouchableOpacity style={styles.actionButton} onPress={onContact}>
            <Phone size={18} color={theme.colors.primary[600]} />
            <Text style={styles.actionButtonText}>Call</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={onMessage}>
            <MessageSquare size={18} color={theme.colors.primary[600]} />
            <Text style={styles.actionButtonText}>Message</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Notes */}
      {booking.notes && (
        <View style={styles.notesSection}>
          <Text style={styles.notesLabel}>Notes:</Text>
          <Text style={styles.notesText}>{booking.notes}</Text>
        </View>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
  },
  statusBadge: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs / 2,
    borderRadius: theme.borderRadius.full,
  },
  statusText: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.white,
  },
  providerSection: {
    flexDirection: 'row',
    marginBottom: theme.spacing.md,
    paddingRight: theme.spacing.xl,
  },
  providerAvatar: {
    width: 50,
    height: 50,
    borderRadius: theme.borderRadius.full,
    marginRight: theme.spacing.md,
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontFamily: 'Inter-SemiBold',
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.xs / 2,
  },
  serviceName: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[600],
    marginBottom: theme.spacing.xs,
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[700],
    marginLeft: theme.spacing.xs / 2,
  },
  detailsSection: {
    marginBottom: theme.spacing.md,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  detailText: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[700],
    marginLeft: theme.spacing.sm,
    flex: 1,
  },
  paymentSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray[200],
    marginBottom: theme.spacing.md,
  },
  paymentAmount: {
    fontFamily: 'Inter-Bold',
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.gray[900],
  },
  paymentStatus: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.sm,
  },
  actionsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray[200],
    marginBottom: theme.spacing.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
  },
  actionButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.primary[600],
    marginLeft: theme.spacing.sm,
  },
  notesSection: {
    backgroundColor: theme.colors.gray[50],
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginTop: theme.spacing.sm,
  },
  notesLabel: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[700],
    marginBottom: theme.spacing.xs,
  },
  notesText: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[600],
    lineHeight: 20,
  },
});