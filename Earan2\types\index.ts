// Core Types
export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: 'customer' | 'provider';
  profileImage?: string;
  isVerified: boolean;
  createdAt: string;
  location?: Location;
}

export interface ServiceProvider extends User {
  professionalTitle: string;
  experience: number;
  serviceCategories: string[];
  serviceRadius: number;
  rating: number;
  reviewCount: number;
  portfolioImages: string[];
  workingHours: WorkingHours;
  isAvailable: boolean;
  completedJobs: number;
  responseTime: number; // in minutes
  pricing: ServicePricing[];
}

export interface Location {
  address: string;
  coordinates: [number, number]; // [longitude, latitude]
  city: string;
  region: string;
  country: string;
}

export interface WorkingHours {
  monday: DaySchedule;
  tuesday: DaySchedule;
  wednesday: DaySchedule;
  thursday: DaySchedule;
  friday: DaySchedule;
  saturday: DaySchedule;
  sunday: DaySchedule;
}

export interface DaySchedule {
  isWorking: boolean;
  startTime: string; // "09:00"
  endTime: string; // "17:00"
  breaks?: TimeSlot[];
}

export interface TimeSlot {
  startTime: string;
  endTime: string;
}

export interface ServiceCategory {
  id: string;
  name: string;
  icon: string;
  description: string;
  subcategories: SubCategory[];
  averagePrice: PriceRange;
  popularityRank: number;
}

export interface SubCategory {
  id: string;
  name: string;
  description: string;
  estimatedDuration: number; // in minutes
  priceRange: PriceRange;
}

export interface PriceRange {
  min: number;
  max: number;
  currency: string;
}

export interface ServicePricing {
  subcategoryId: string;
  basePrice: number;
  currency: string;
  priceType: 'fixed' | 'hourly' | 'quote';
  additionalCharges?: AdditionalCharge[];
}

export interface AdditionalCharge {
  name: string;
  amount: number;
  type: 'fixed' | 'percentage';
}

export interface ServiceRequest {
  id: string;
  customerId: string;
  customerName: string;
  customerRating: number;
  customerPhone: string;
  serviceType: string;
  subcategory: string;
  location: Location;
  scheduledTime: string;
  estimatedDuration: number;
  priceEstimate: number;
  status: BookingStatus;
  specialRequirements?: string;
  attachments?: string[];
  urgency: 'low' | 'medium' | 'high' | 'emergency';
  createdAt: string;
}

export type BookingStatus = 
  | 'pending' 
  | 'accepted' 
  | 'confirmed'
  | 'in_progress' 
  | 'completed' 
  | 'cancelled'
  | 'refunded';

export interface Booking {
  id: string;
  customerId: string;
  providerId: string;
  serviceRequest: ServiceRequest;
  acceptedAt?: string;
  startedAt?: string;
  completedAt?: string;
  cancelledAt?: string;
  cancellationReason?: string;
  finalPrice: number;
  paymentStatus: PaymentStatus;
  rating?: number;
  review?: Review;
}

export type PaymentStatus = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'failed' 
  | 'refunded';

export interface PaymentMethod {
  id: string;
  type: 'card' | 'mobile_money' | 'bank_transfer';
  lastFour?: string;
  provider: string; // Visa, MTN, Vodafone, etc.
  isDefault: boolean;
  metadata?: Record<string, any>;
}

export interface Transaction {
  id: string;
  bookingId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: PaymentMethod;
  reference: string;
  serviceDetails: {
    providerName: string;
    serviceType: string;
    duration: number;
  };
  fees: {
    platformFee: number;
    processingFee: number;
    taxes: number;
  };
  timestamp: string;
}

export interface Review {
  id: string;
  bookingId: string;
  reviewerId: string;
  reviewerName: string;
  reviewerImage?: string;
  targetId: string; // provider or customer ID
  targetType: 'provider' | 'customer';
  rating: number;
  comment: string;
  photos?: string[];
  categoryRatings: {
    punctuality: number;
    quality: number;
    professionalism: number;
    communication: number;
  };
  createdAt: string;
  response?: ReviewResponse;
}

export interface ReviewResponse {
  comment: string;
  createdAt: string;
}

// Authentication Types
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  phone: string;
  countryCode: string;
}

export interface OTPVerification {
  phone: string;
  countryCode: string;
  otp: string;
}

export interface RegistrationData {
  name: string;
  email: string;
  phone: string;
  countryCode: string;
  role: 'customer' | 'provider';
  profileImage?: string;
}

// UI Types
export interface TabBarIconProps {
  focused: boolean;
  color: string;
  size: number;
}

export interface ButtonProps {
  variant: 'primary' | 'secondary' | 'ghost' | 'outline';
  size: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  onPress: () => void;
  children: React.ReactNode;
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

export interface ServiceCardProps {
  provider: ServiceProvider;
  distance: number;
  onPress: () => void;
  onFavorite: () => void;
  isFavorite?: boolean;
}

export interface MapViewProps {
  providers: ServiceProvider[];
  userLocation: [number, number];
  onProviderSelect: (provider: ServiceProvider) => void;
  trackingMode?: boolean;
  selectedProvider?: ServiceProvider;
}

// Navigation Types
export type RootStackParamList = {
  '(tabs)': undefined;
  'auth/welcome': undefined;
  'auth/phone': undefined;
  'auth/otp': { phone: string; countryCode: string };
  'auth/register': { phone: string; countryCode: string };
  'provider/[id]': { id: string };
  'booking/create': { providerId: string; serviceId: string };
  'booking/confirmation': { bookingId: string };
  'chat/[id]': { id: string };
};

export type TabParamList = {
  'index': undefined;
  'search': undefined;
  'bookings': undefined;
  'messages': undefined;
  'profile': undefined;
};