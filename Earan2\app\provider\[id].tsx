import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView,
  Image,
  TouchableOpacity,
  FlatList,
  Dimensions
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { 
  ArrowLeft, 
  Star, 
  MapPin, 
  Clock, 
  Shield, 
  Heart,
  MessageCircle,
  Phone
} from 'lucide-react-native';
import { colors, spacing, typography, borderRadius } from '@/constants/theme';
import { MOCK_PROVIDERS } from '@/data/mockProviders';
import { ServiceProvider } from '@/types';
import Button from '@/components/common/Button';

const { width } = Dimensions.get('window');

export default function ProviderScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [provider, setProvider] = useState<ServiceProvider | null>(null);
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  useEffect(() => {
    const foundProvider = MOCK_PROVIDERS.find(p => p.id === id);
    setProvider(foundProvider || null);
  }, [id]);

  if (!provider) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Provider not found</Text>
      </SafeAreaView>
    );
  }

  const renderPortfolioImage = ({ item, index }: { item: string; index: number }) => (
    <TouchableOpacity onPress={() => setSelectedImageIndex(index)}>
      <Image source={{ uri: item }} style={styles.portfolioImage} />
    </TouchableOpacity>
  );

  const renderReview = ({ item }: { item: any }) => (
    <View style={styles.reviewCard}>
      <View style={styles.reviewHeader}>
        <Image source={{ uri: item.avatar }} style={styles.reviewerAvatar} />
        <View style={styles.reviewerInfo}>
          <Text style={styles.reviewerName}>{item.name}</Text>
          <View style={styles.reviewRating}>
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                size={12}
                color={i < item.rating ? colors.warning[500] : colors.gray[300]}
                fill={i < item.rating ? colors.warning[500] : colors.gray[300]}
              />
            ))}
          </View>
        </View>
        <Text style={styles.reviewDate}>{item.date}</Text>
      </View>
      <Text style={styles.reviewText}>{item.comment}</Text>
    </View>
  );

  const mockReviews = [
    {
      id: '1',
      name: 'Sarah Johnson',
      avatar: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=100',
      rating: 5,
      date: '2 days ago',
      comment: 'Excellent work! Very professional and completed the job on time.',
    },
    {
      id: '2',
      name: 'Michael Brown',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100',
      rating: 4,
      date: '1 week ago',
      comment: 'Good service, would recommend to others.',
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header Image */}
        <View style={styles.headerImageContainer}>
          <Image 
            source={{ uri: provider.portfolioImages[selectedImageIndex] }} 
            style={styles.headerImage} 
          />
          
          <View style={styles.headerOverlay}>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color={colors.white} />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.favoriteHeaderButton}
              onPress={() => setIsFavorite(!isFavorite)}
            >
              <Heart 
                size={24} 
                color={isFavorite ? colors.error[500] : colors.white}
                fill={isFavorite ? colors.error[500] : 'transparent'}
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Provider Info */}
        <View style={styles.providerInfo}>
          <View style={styles.providerHeader}>
            <Image source={{ uri: provider.profileImage }} style={styles.profileImage} />
            <View style={styles.providerDetails}>
              <Text style={styles.providerName}>{provider.name}</Text>
              <Text style={styles.providerTitle}>{provider.professionalTitle}</Text>
              
              <View style={styles.providerMeta}>
                <View style={styles.ratingContainer}>
                  <Star size={16} color={colors.warning[500]} fill={colors.warning[500]} />
                  <Text style={styles.ratingText}>{provider.rating}</Text>
                  <Text style={styles.reviewCount}>({provider.reviewCount} reviews)</Text>
                </View>
                
                <View style={styles.locationContainer}>
                  <MapPin size={14} color={colors.gray[500]} />
                  <Text style={styles.locationText}>{provider.location?.city}</Text>
                </View>
              </View>
            </View>
            
            <View style={[
              styles.availabilityBadge,
              provider.isAvailable ? styles.availableBadge : styles.unavailableBadge
            ]}>
              <Text style={[
                styles.availabilityText,
                provider.isAvailable ? styles.availableText : styles.unavailableText
              ]}>
                {provider.isAvailable ? 'Available' : 'Busy'}
              </Text>
            </View>
          </View>

          {/* Stats */}
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{provider.completedJobs}</Text>
              <Text style={styles.statLabel}>Jobs Done</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{provider.experience}</Text>
              <Text style={styles.statLabel}>Years Exp</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{provider.responseTime}m</Text>
              <Text style={styles.statLabel}>Response</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{provider.serviceRadius}km</Text>
              <Text style={styles.statLabel}>Radius</Text>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <Button
              variant="primary"
              size="lg"
              onPress={() => router.push({
                pathname: '/booking/create',
                params: { providerId: provider.id }
              })}
            >
              Book Now
            </Button>
            
            <TouchableOpacity style={styles.messageButton}>
              <MessageCircle size={20} color={colors.primary[600]} />
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.callButton}>
              <Phone size={20} color={colors.primary[600]} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Portfolio */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Portfolio</Text>
          <FlatList
            data={provider.portfolioImages}
            renderItem={renderPortfolioImage}
            keyExtractor={(item, index) => index.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.portfolioContainer}
          />
        </View>

        {/* Services */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Services & Pricing</Text>
          {provider.pricing.map((service, index) => (
            <View key={index} style={styles.serviceItem}>
              <View style={styles.serviceInfo}>
                <Text style={styles.serviceName}>Service {index + 1}</Text>
                <Text style={styles.serviceDescription}>Professional service description</Text>
              </View>
              <Text style={styles.servicePrice}>
                GHS {service.basePrice}
                {service.priceType === 'hourly' && '/hr'}
              </Text>
            </View>
          ))}
        </View>

        {/* Reviews */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Reviews</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={mockReviews}
            renderItem={renderReview}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
          />
        </View>

        {/* Working Hours */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Working Hours</Text>
          <View style={styles.workingHours}>
            {Object.entries(provider.workingHours).map(([day, schedule]) => (
              <View key={day} style={styles.workingHourRow}>
                <Text style={styles.dayText}>
                  {day.charAt(0).toUpperCase() + day.slice(1)}
                </Text>
                <Text style={styles.timeText}>
                  {schedule.isWorking 
                    ? `${schedule.startTime} - ${schedule.endTime}`
                    : 'Closed'
                  }
                </Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
  },
  headerImageContainer: {
    position: 'relative',
  },
  headerImage: {
    width: '100%',
    height: 250,
  },
  headerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: spacing.xl,
    paddingTop: spacing.lg,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteHeaderButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  providerInfo: {
    backgroundColor: colors.white,
    borderTopLeftRadius: borderRadius.xl,
    borderTopRightRadius: borderRadius.xl,
    marginTop: -20,
    padding: spacing.xl,
  },
  providerHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.xl,
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: spacing.lg,
  },
  providerDetails: {
    flex: 1,
  },
  providerName: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: 'Inter-Bold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  providerTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
    marginBottom: spacing.md,
  },
  providerMeta: {
    gap: spacing.sm,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  ratingText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  reviewCount: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  locationText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  availabilityBadge: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.full,
  },
  availableBadge: {
    backgroundColor: colors.success[100],
  },
  unavailableBadge: {
    backgroundColor: colors.gray[100],
  },
  availabilityText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-SemiBold',
  },
  availableText: {
    color: colors.success[700],
  },
  unavailableText: {
    color: colors.gray[600],
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.gray[50],
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    marginBottom: spacing.xl,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: typography.fontSize.xl,
    fontFamily: 'Inter-Bold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  actionButtons: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.xl,
  },
  messageButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary[50],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.primary[200],
  },
  callButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary[50],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.primary[200],
  },
  section: {
    paddingHorizontal: spacing.xl,
    marginBottom: spacing['2xl'],
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.lg,
  },
  seeAllText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Medium',
    color: colors.primary[600],
  },
  portfolioContainer: {
    paddingRight: spacing.xl,
    gap: spacing.md,
  },
  portfolioImage: {
    width: 120,
    height: 120,
    borderRadius: borderRadius.md,
  },
  serviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  serviceDescription: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  servicePrice: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.primary[600],
  },
  reviewCard: {
    backgroundColor: colors.gray[50],
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    marginBottom: spacing.md,
  },
  reviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  reviewerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: spacing.md,
  },
  reviewerInfo: {
    flex: 1,
  },
  reviewerName: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  reviewRating: {
    flexDirection: 'row',
    gap: spacing.xs,
  },
  reviewDate: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  reviewText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[700],
    lineHeight: 24,
  },
  workingHours: {
    backgroundColor: colors.gray[50],
    borderRadius: borderRadius.md,
    padding: spacing.lg,
  },
  workingHourRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacing.sm,
  },
  dayText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Medium',
    color: colors.gray[900],
  },
  timeText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
  },
});