import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Star, MapPin } from 'lucide-react-native';
import { ServiceProvider } from '../../types';
import { theme } from '../../theme/colors';

interface ServiceCardProps {
  provider: ServiceProvider;
  onPress: () => void;
}

export function ServiceCard({ provider, onPress }: ServiceCardProps) {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <Image
        source={{
          uri: provider.user.avatar || 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=400',
        }}
        style={styles.avatar}
      />
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.name}>{provider.user.name}</Text>
          <View style={styles.rating}>
            <Star size={14} color={theme.colors.accent[500]} fill={theme.colors.accent[500]} />
            <Text style={styles.ratingText}>{provider.rating.toFixed(1)}</Text>
          </View>
        </View>
        <Text style={styles.services}>
          {provider.services.map(s => s.name).join(', ')}
        </Text>
        <View style={styles.footer}>
          <View style={styles.location}>
            <MapPin size={12} color={theme.colors.gray[500]} />
            <Text style={styles.locationText}>
              {provider.location.address || 'Location available'}
            </Text>
          </View>
          <Text style={styles.price}>
            ₵{provider.pricing.min} - ₵{provider.pricing.max}
          </Text>
        </View>
      </View>
      {provider.isAvailable && (
        <View style={styles.availableBadge}>
          <Text style={styles.availableText}>Available</Text>
        </View>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    flexDirection: 'row',
    ...theme.shadows.md,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: theme.borderRadius.full,
    marginRight: theme.spacing.md,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  name: {
    fontFamily: 'Inter-SemiBold',
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.gray[900],
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[700],
    marginLeft: theme.spacing.xs / 2,
  },
  services: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[600],
    marginBottom: theme.spacing.sm,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  location: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  locationText: {
    fontFamily: 'Inter-Regular',
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.gray[500],
    marginLeft: theme.spacing.xs / 2,
  },
  price: {
    fontFamily: 'Inter-SemiBold',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.primary[600],
  },
  availableBadge: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
    backgroundColor: theme.colors.success[500],
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs / 2,
    borderRadius: theme.borderRadius.full,
  },
  availableText: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.white,
  },
});