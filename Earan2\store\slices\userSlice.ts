import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Location } from '@/types';

interface UserState {
  currentLocation: Location | null;
  favoriteProviders: string[];
  recentSearches: string[];
  isLocationPermissionGranted: boolean;
  preferences: {
    currency: string;
    language: string;
    notifications: {
      push: boolean;
      email: boolean;
      sms: boolean;
    };
  };
}

const initialState: UserState = {
  currentLocation: null,
  favoriteProviders: [],
  recentSearches: [],
  isLocationPermissionGranted: false,
  preferences: {
    currency: 'GHS',
    language: 'en',
    notifications: {
      push: true,
      email: true,
      sms: false,
    },
  },
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setCurrentLocation: (state, action: PayloadAction<Location>) => {
      state.currentLocation = action.payload;
    },
    setLocationPermission: (state, action: PayloadAction<boolean>) => {
      state.isLocationPermissionGranted = action.payload;
    },
    addFavoriteProvider: (state, action: PayloadAction<string>) => {
      if (!state.favoriteProviders.includes(action.payload)) {
        state.favoriteProviders.push(action.payload);
      }
    },
    removeFavoriteProvider: (state, action: PayloadAction<string>) => {
      state.favoriteProviders = state.favoriteProviders.filter(
        (id) => id !== action.payload
      );
    },
    addRecentSearch: (state, action: PayloadAction<string>) => {
      const search = action.payload;
      state.recentSearches = [
        search,
        ...state.recentSearches.filter((s) => s !== search),
      ].slice(0, 10);
    },
    clearRecentSearches: (state) => {
      state.recentSearches = [];
    },
    updatePreferences: (state, action: PayloadAction<Partial<UserState['preferences']>>) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },
  },
});

export const {
  setCurrentLocation,
  setLocationPermission,
  addFavoriteProvider,
  removeFavoriteProvider,
  addRecentSearch,
  clearRecentSearches,
  updatePreferences,
} = userSlice.actions;

export default userSlice.reducer;