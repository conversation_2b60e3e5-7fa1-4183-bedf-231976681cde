import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { ServiceCategory } from '../../types';
import { theme } from '../../theme/colors';

interface CategoryCardProps {
  category: ServiceCategory;
  onPress: () => void;
}

export function CategoryCard({ category, onPress }: CategoryCardProps) {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={[styles.iconContainer, { backgroundColor: category.color }]}>
        <Text style={styles.icon}>{category.icon}</Text>
      </View>
      <Text style={styles.name}>{category.name}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: theme.spacing.md,
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    marginRight: theme.spacing.md,
    minWidth: 100,
    ...theme.shadows.sm,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: theme.borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.sm,
  },
  icon: {
    fontSize: 24,
  },
  name: {
    fontFamily: 'Inter-Medium',
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[700],
    textAlign: 'center',
  },
});