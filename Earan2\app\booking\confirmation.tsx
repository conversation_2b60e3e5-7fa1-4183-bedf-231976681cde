import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView,
  TouchableOpacity,
  Image
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { CheckCircle, Calendar, Clock, MapPin, Phone, MessageCircle } from 'lucide-react-native';
import { colors, spacing, typography, borderRadius } from '@/constants/theme';
import Button from '@/components/common/Button';
import dayjs from 'dayjs';

interface BookingDetails {
  id: string;
  providerName: string;
  providerImage: string;
  providerPhone: string;
  serviceType: string;
  scheduledDate: string;
  scheduledTime: string;
  location: string;
  totalPrice: number;
  status: string;
}

export default function BookingConfirmationScreen() {
  const { bookingId } = useLocalSearchParams<{ bookingId: string }>();
  const [booking, setBooking] = useState<BookingDetails | null>(null);

  useEffect(() => {
    // Simulate fetching booking details
    const mockBooking: BookingDetails = {
      id: bookingId || '1',
      providerName: 'Kwame Asante',
      providerImage: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=400',
      providerPhone: '+233 24 123 4567',
      serviceType: 'Plumbing Repair',
      scheduledDate: dayjs().add(1, 'day').format('YYYY-MM-DD'),
      scheduledTime: '14:00',
      location: 'East Legon, Accra',
      totalPrice: 126.00,
      status: 'confirmed',
    };
    
    setBooking(mockBooking);
  }, [bookingId]);

  if (!booking) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Loading...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Success Icon */}
        <View style={styles.successSection}>
          <CheckCircle size={80} color={colors.success[500]} />
          <Text style={styles.successTitle}>Booking Confirmed!</Text>
          <Text style={styles.successSubtitle}>
            Your service has been booked successfully
          </Text>
        </View>

        {/* Booking Details */}
        <View style={styles.detailsCard}>
          <Text style={styles.cardTitle}>Booking Details</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Booking ID</Text>
            <Text style={styles.detailValue}>#{booking.id.toUpperCase()}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Service</Text>
            <Text style={styles.detailValue}>{booking.serviceType}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Calendar size={16} color={colors.gray[500]} />
            <Text style={styles.detailLabel}>Date</Text>
            <Text style={styles.detailValue}>
              {dayjs(booking.scheduledDate).format('MMM DD, YYYY')}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Clock size={16} color={colors.gray[500]} />
            <Text style={styles.detailLabel}>Time</Text>
            <Text style={styles.detailValue}>{booking.scheduledTime}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <MapPin size={16} color={colors.gray[500]} />
            <Text style={styles.detailLabel}>Location</Text>
            <Text style={styles.detailValue}>{booking.location}</Text>
          </View>
          
          <View style={[styles.detailRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total Amount</Text>
            <Text style={styles.totalValue}>GHS {booking.totalPrice}</Text>
          </View>
        </View>

        {/* Provider Info */}
        <View style={styles.providerCard}>
          <Text style={styles.cardTitle}>Service Provider</Text>
          
          <View style={styles.providerInfo}>
            <Image source={{ uri: booking.providerImage }} style={styles.providerImage} />
            <View style={styles.providerDetails}>
              <Text style={styles.providerName}>{booking.providerName}</Text>
              <Text style={styles.providerPhone}>{booking.providerPhone}</Text>
            </View>
          </View>
          
          <View style={styles.contactButtons}>
            <TouchableOpacity style={styles.contactButton}>
              <Phone size={20} color={colors.primary[600]} />
              <Text style={styles.contactButtonText}>Call</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.contactButton}
              onPress={() => router.push(`/chat/${booking.id}`)}
            >
              <MessageCircle size={20} color={colors.primary[600]} />
              <Text style={styles.contactButtonText}>Message</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Next Steps */}
        <View style={styles.nextStepsCard}>
          <Text style={styles.cardTitle}>What's Next?</Text>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>1</Text>
            </View>
            <Text style={styles.stepText}>
              The service provider will contact you to confirm details
            </Text>
          </View>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>2</Text>
            </View>
            <Text style={styles.stepText}>
              You'll receive a notification when they're on their way
            </Text>
          </View>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>3</Text>
            </View>
            <Text style={styles.stepText}>
              Rate and review the service after completion
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <Button
          variant="outline"
          size="lg"
          onPress={() => router.push('/(tabs)/bookings')}
        >
          View Bookings
        </Button>
        
        <Button
          variant="primary"
          size="lg"
          onPress={() => router.push('/(tabs)')}
        >
          Back to Home
        </Button>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: spacing['3xl'],
  },
  successSection: {
    alignItems: 'center',
    paddingVertical: spacing['3xl'],
    paddingHorizontal: spacing.xl,
  },
  successTitle: {
    fontSize: typography.fontSize['3xl'],
    fontFamily: 'Inter-Bold',
    color: colors.gray[900],
    marginTop: spacing.xl,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  successSubtitle: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
    textAlign: 'center',
  },
  detailsCard: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    padding: spacing.xl,
    marginHorizontal: spacing.xl,
    marginBottom: spacing.xl,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  providerCard: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    padding: spacing.xl,
    marginHorizontal: spacing.xl,
    marginBottom: spacing.xl,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  nextStepsCard: {
    backgroundColor: colors.primary[50],
    borderRadius: borderRadius.lg,
    padding: spacing.xl,
    marginHorizontal: spacing.xl,
  },
  cardTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.lg,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    gap: spacing.sm,
  },
  detailLabel: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
    flex: 1,
  },
  detailValue: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    paddingTop: spacing.md,
    marginTop: spacing.md,
    marginBottom: 0,
  },
  totalLabel: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    flex: 1,
  },
  totalValue: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-Bold',
    color: colors.primary[600],
  },
  providerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  providerImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: spacing.md,
  },
  providerDetails: {
    flex: 1,
  },
  providerName: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  providerPhone: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
  },
  contactButtons: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  contactButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.primary[200],
    backgroundColor: colors.primary[50],
    gap: spacing.sm,
  },
  contactButtonText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.primary[600],
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
    gap: spacing.md,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.primary[600],
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepNumberText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-SemiBold',
    color: colors.white,
  },
  stepText: {
    flex: 1,
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[700],
    lineHeight: 24,
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    gap: spacing.md,
  },
});