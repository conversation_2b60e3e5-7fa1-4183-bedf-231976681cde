import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Clock, MapPin, Star, ChevronRight } from 'lucide-react-native';
import { colors, spacing, typography, borderRadius } from '@/constants/theme';

interface Booking {
  id: string;
  providerId: string;
  providerName: string;
  providerImage: string;
  serviceType: string;
  status: 'upcoming' | 'in_progress' | 'completed' | 'cancelled';
  scheduledDate: string;
  scheduledTime: string;
  location: string;
  price: number;
  rating?: number;
}

const mockBookings: Booking[] = [
  {
    id: '1',
    providerId: '1',
    providerName: 'Kwame Asante',
    providerImage: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=400',
    serviceType: 'Plumbing Repair',
    status: 'upcoming',
    scheduledDate: 'Today',
    scheduledTime: '2:00 PM',
    location: 'East Legon, Accra',
    price: 120,
  },
  {
    id: '2',
    providerId: '2',
    providerName: 'Akosua Mensah',
    providerImage: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400',
    serviceType: 'Electrical Installation',
    status: 'in_progress',
    scheduledDate: 'Today',
    scheduledTime: '10:00 AM',
    location: 'Cantonments, Accra',
    price: 200,
  },
  {
    id: '3',
    providerId: '3',
    providerName: 'Kofi Boateng',
    providerImage: 'https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg?auto=compress&cs=tinysrgb&w=400',
    serviceType: 'Furniture Repair',
    status: 'completed',
    scheduledDate: 'Yesterday',
    scheduledTime: '3:00 PM',
    location: 'Airport Hills, Accra',
    price: 150,
    rating: 5,
  },
  {
    id: '4',
    providerId: '4',
    providerName: 'Ama Osei',
    providerImage: 'https://images.pexels.com/photos/1181424/pexels-photo-1181424.jpeg?auto=compress&cs=tinysrgb&w=400',
    serviceType: 'House Cleaning',
    status: 'completed',
    scheduledDate: '3 days ago',
    scheduledTime: '9:00 AM',
    location: 'Labone, Accra',
    price: 80,
    rating: 4,
  },
];

export default function BookingsScreen() {
  const [selectedTab, setSelectedTab] = useState<'active' | 'completed'>('active');
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 1000);
  };

  const getStatusColor = (status: Booking['status']) => {
    switch (status) {
      case 'upcoming':
        return colors.primary[600];
      case 'in_progress':
        return colors.warning[600];
      case 'completed':
        return colors.success[600];
      case 'cancelled':
        return colors.error[600];
      default:
        return colors.gray[600];
    }
  };

  const getStatusText = (status: Booking['status']) => {
    switch (status) {
      case 'upcoming':
        return 'Upcoming';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const activeBookings = mockBookings.filter(b => 
    b.status === 'upcoming' || b.status === 'in_progress'
  );
  
  const completedBookings = mockBookings.filter(b => 
    b.status === 'completed' || b.status === 'cancelled'
  );

  const currentBookings = selectedTab === 'active' ? activeBookings : completedBookings;

  const renderBooking = ({ item }: { item: Booking }) => (
    <TouchableOpacity 
      style={styles.bookingCard}
      onPress={() => router.push(`/booking/${item.id}`)}
    >
      <View style={styles.bookingHeader}>
        <Image source={{ uri: item.providerImage }} style={styles.providerImage} />
        <View style={styles.bookingInfo}>
          <Text style={styles.providerName}>{item.providerName}</Text>
          <Text style={styles.serviceType}>{item.serviceType}</Text>
          <View style={styles.bookingMeta}>
            <Clock size={14} color={colors.gray[500]} />
            <Text style={styles.metaText}>{item.scheduledDate} • {item.scheduledTime}</Text>
          </View>
          <View style={styles.bookingMeta}>
            <MapPin size={14} color={colors.gray[500]} />
            <Text style={styles.metaText}>{item.location}</Text>
          </View>
        </View>
        <View style={styles.bookingRight}>
          <View style={[
            styles.statusBadge,
            { backgroundColor: `${getStatusColor(item.status)}20` }
          ]}>
            <Text style={[
              styles.statusText,
              { color: getStatusColor(item.status) }
            ]}>
              {getStatusText(item.status)}
            </Text>
          </View>
          <ChevronRight size={20} color={colors.gray[400]} />
        </View>
      </View>

      <View style={styles.bookingFooter}>
        <Text style={styles.priceText}>GHS {item.price}</Text>
        {item.rating && (
          <View style={styles.ratingContainer}>
            <Star size={14} color={colors.warning[500]} fill={colors.warning[500]} />
            <Text style={styles.ratingText}>{item.rating}.0</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateText}>
        {selectedTab === 'active' 
          ? 'No active bookings' 
          : 'No completed bookings'
        }
      </Text>
      <Text style={styles.emptyStateSubtext}>
        {selectedTab === 'active' 
          ? 'Book a service to get started' 
          : 'Your booking history will appear here'
        }
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>My Bookings</Text>
      </View>

      {/* Tabs */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            selectedTab === 'active' && styles.activeTab
          ]}
          onPress={() => setSelectedTab('active')}
        >
          <Text style={[
            styles.tabText,
            selectedTab === 'active' && styles.activeTabText
          ]}>
            Active ({activeBookings.length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            selectedTab === 'completed' && styles.activeTab
          ]}
          onPress={() => setSelectedTab('completed')}
        >
          <Text style={[
            styles.tabText,
            selectedTab === 'completed' && styles.activeTabText
          ]}>
            History ({completedBookings.length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Bookings List */}
      <FlatList
        data={currentBookings}
        renderItem={renderBooking}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.bookingsList}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
  },
  title: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: 'Inter-Bold',
    color: colors.gray[900],
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.xl,
    marginBottom: spacing.lg,
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: colors.gray[200],
  },
  activeTab: {
    borderBottomColor: colors.primary[600],
  },
  tabText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Medium',
    color: colors.gray[500],
  },
  activeTabText: {
    color: colors.primary[600],
  },
  bookingsList: {
    paddingHorizontal: spacing.xl,
    paddingBottom: spacing['3xl'],
  },
  bookingCard: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    marginBottom: spacing.lg,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  bookingHeader: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  providerImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: spacing.md,
  },
  bookingInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  serviceType: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
    marginBottom: spacing.sm,
  },
  bookingMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    marginBottom: spacing.xs,
  },
  metaText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  bookingRight: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  statusBadge: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.full,
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontFamily: 'Inter-SemiBold',
  },
  bookingFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.gray[100],
  },
  priceText: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  ratingText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing['4xl'],
  },
  emptyStateText: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.sm,
  },
  emptyStateSubtext: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
    textAlign: 'center',
  },
});