import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Booking, ServiceRequest } from '@/types';

interface BookingsState {
  currentBookings: Booking[];
  pastBookings: Booking[];
  pendingRequests: ServiceRequest[];
  activeBooking: Booking | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: BookingsState = {
  currentBookings: [],
  pastBookings: [],
  pendingRequests: [],
  activeBooking: null,
  isLoading: false,
  error: null,
};

const bookingsSlice = createSlice({
  name: 'bookings',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setCurrentBookings: (state, action: PayloadAction<Booking[]>) => {
      state.currentBookings = action.payload;
    },
    setPastBookings: (state, action: PayloadAction<Booking[]>) => {
      state.pastBookings = action.payload;
    },
    setPendingRequests: (state, action: PayloadAction<ServiceRequest[]>) => {
      state.pendingRequests = action.payload;
    },
    setActiveBooking: (state, action: PayloadAction<Booking | null>) => {
      state.activeBooking = action.payload;
    },
    addBooking: (state, action: PayloadAction<Booking>) => {
      state.currentBookings.unshift(action.payload);
    },
    updateBooking: (state, action: PayloadAction<{ id: string; updates: Partial<Booking> }>) => {
      const { id, updates } = action.payload;
      
      // Update in current bookings
      const currentIndex = state.currentBookings.findIndex(b => b.id === id);
      if (currentIndex !== -1) {
        state.currentBookings[currentIndex] = { ...state.currentBookings[currentIndex], ...updates };
        
        // Move to past bookings if completed or cancelled
        if (updates.status === 'completed' || updates.status === 'cancelled') {
          const booking = state.currentBookings.splice(currentIndex, 1)[0];
          state.pastBookings.unshift(booking);
        }
      }
      
      // Update active booking if it's the same
      if (state.activeBooking?.id === id) {
        state.activeBooking = { ...state.activeBooking, ...updates };
      }
    },
    removeBooking: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      state.currentBookings = state.currentBookings.filter(b => b.id !== id);
      state.pastBookings = state.pastBookings.filter(b => b.id !== id);
      
      if (state.activeBooking?.id === id) {
        state.activeBooking = null;
      }
    },
    addPendingRequest: (state, action: PayloadAction<ServiceRequest>) => {
      state.pendingRequests.unshift(action.payload);
    },
    removePendingRequest: (state, action: PayloadAction<string>) => {
      state.pendingRequests = state.pendingRequests.filter(r => r.id !== action.payload);
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setLoading,
  setError,
  setCurrentBookings,
  setPastBookings,
  setPendingRequests,
  setActiveBooking,
  addBooking,
  updateBooking,
  removeBooking,
  addPendingRequest,
  removePendingRequest,
  clearError,
} = bookingsSlice.actions;

export default bookingsSlice.reducer;