import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { TouchableOpacity } from 'react-native';
import { colors, spacing, typography } from '@/constants/theme';
import Button from '@/components/common/Button';
import { useAppDispatch } from '@/store/hooks';
import { loginSuccess } from '@/store/slices/authSlice';

export default function OTPScreen() {
  const { phone, countryCode } = useLocalSearchParams<{ phone: string; countryCode: string }>();
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60);
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [timeLeft]);

  const handleOtpChange = (value: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
  };

  const handleVerifyOTP = async () => {
    const otpString = otp.join('');
    if (otpString.length !== 6) {
      Alert.alert('Error', 'Please enter the complete verification code');
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      
      // Mock successful login
      const mockUser = {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: `${countryCode}${phone}`,
        role: 'customer' as const,
        isVerified: true,
        createdAt: new Date().toISOString(),
      };

      dispatch(loginSuccess({
        user: mockUser,
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
      }));

      router.replace('/(tabs)');
    }, 1500);
  };

  const handleResendOTP = () => {
    setTimeLeft(60);
    // Simulate API call to resend OTP
    Alert.alert('Success', 'Verification code sent successfully');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft color={colors.gray[600]} size={24} />
        </TouchableOpacity>
        <Text style={styles.title}>Verify your number</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.description}>
          Enter the 6-digit code sent to {countryCode} {phone}
        </Text>

        <View style={styles.otpContainer}>
          {otp.map((digit, index) => (
            <View key={index} style={styles.otpInput}>
              <Text style={styles.otpText}>{digit}</Text>
            </View>
          ))}
        </View>

        <View style={styles.keypad}>
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, '', 0, '⌫'].map((key, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.keypadButton, key === '' && styles.emptyKey]}
              onPress={() => {
                if (key === '') return;
                if (key === '⌫') {
                  // Handle backspace
                  const lastFilledIndex = otp.findLastIndex(digit => digit !== '');
                  if (lastFilledIndex >= 0) {
                    handleOtpChange('', lastFilledIndex);
                  }
                } else {
                  // Handle number input
                  const firstEmptyIndex = otp.findIndex(digit => digit === '');
                  if (firstEmptyIndex >= 0) {
                    handleOtpChange(key.toString(), firstEmptyIndex);
                  }
                }
              }}
              disabled={key === ''}
            >
              <Text style={styles.keypadText}>{key}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Button
          variant="primary"
          size="lg"
          onPress={handleVerifyOTP}
          loading={isLoading}
          disabled={otp.join('').length !== 6}
          fullWidth
        >
          Verify Code
        </Button>

        <View style={styles.resendContainer}>
          {timeLeft > 0 ? (
            <Text style={styles.timerText}>
              Resend code in {timeLeft}s
            </Text>
          ) : (
            <TouchableOpacity onPress={handleResendOTP}>
              <Text style={styles.resendText}>Resend Code</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    gap: spacing.md,
  },
  backButton: {
    padding: spacing.sm,
  },
  title: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing['2xl'],
  },
  description: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
    marginBottom: spacing['3xl'],
    textAlign: 'center',
    lineHeight: 24,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing.md,
    marginBottom: spacing['3xl'],
  },
  otpInput: {
    width: 50,
    height: 50,
    borderWidth: 2,
    borderColor: colors.primary[600],
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
  },
  otpText: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  keypad: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: spacing.lg,
    marginBottom: spacing['3xl'],
  },
  keypadButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyKey: {
    backgroundColor: 'transparent',
  },
  keypadText: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  resendContainer: {
    alignItems: 'center',
    marginTop: spacing.lg,
  },
  timerText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  resendText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.primary[600],
  },
});