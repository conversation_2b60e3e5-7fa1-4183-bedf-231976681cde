import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList,
  TouchableOpacity,
  Image,
  TextInput
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { Search, Filter, MapPin, Star, Heart, Map } from 'lucide-react-native';
import { colors, spacing, typography, borderRadius } from '@/constants/theme';
import { SERVICE_CATEGORIES } from '@/constants/services';
import { MOCK_PROVIDERS, getProvidersByCategory } from '@/data/mockProviders';
import { ServiceProvider } from '@/types';

export default function SearchScreen() {
  const { category } = useLocalSearchParams<{ category?: string }>();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(category || '');
  const [showMap, setShowMap] = useState(false);
  const [providers, setProviders] = useState<ServiceProvider[]>(MOCK_PROVIDERS);
  const [favorites, setFavorites] = useState<string[]>([]);

  useEffect(() => {
    filterProviders();
  }, [selectedCategory, searchQuery]);

  const filterProviders = () => {
    let filtered = MOCK_PROVIDERS;

    if (selectedCategory) {
      filtered = getProvidersByCategory(selectedCategory);
    }

    if (searchQuery) {
      filtered = filtered.filter(provider => 
        provider.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        provider.professionalTitle.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setProviders(filtered);
  };

  const toggleFavorite = (providerId: string) => {
    setFavorites(prev => 
      prev.includes(providerId) 
        ? prev.filter(id => id !== providerId)
        : [...prev, providerId]
    );
  };

  const renderCategory = ({ item }: { item: typeof SERVICE_CATEGORIES[0] }) => (
    <TouchableOpacity
      style={[
        styles.categoryChip,
        selectedCategory === item.id && styles.categoryChipActive
      ]}
      onPress={() => setSelectedCategory(selectedCategory === item.id ? '' : item.id)}
    >
      <Text style={[
        styles.categoryChipText,
        selectedCategory === item.id && styles.categoryChipTextActive
      ]}>
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  const renderProvider = ({ item }: { item: ServiceProvider }) => (
    <TouchableOpacity 
      style={styles.providerCard}
      onPress={() => router.push(`/provider/${item.id}`)}
    >
      <Image source={{ uri: item.profileImage }} style={styles.providerImage} />
      
      <TouchableOpacity 
        style={styles.favoriteButton}
        onPress={() => toggleFavorite(item.id)}
      >
        <Heart 
          size={20} 
          color={favorites.includes(item.id) ? colors.error[500] : colors.gray[400]}
          fill={favorites.includes(item.id) ? colors.error[500] : 'transparent'}
        />
      </TouchableOpacity>

      <View style={styles.providerInfo}>
        <Text style={styles.providerName}>{item.name}</Text>
        <Text style={styles.providerTitle}>{item.professionalTitle}</Text>
        
        <View style={styles.providerMeta}>
          <View style={styles.ratingContainer}>
            <Star size={14} color={colors.warning[500]} fill={colors.warning[500]} />
            <Text style={styles.ratingText}>{item.rating}</Text>
            <Text style={styles.reviewCount}>({item.reviewCount})</Text>
          </View>
          
          <View style={styles.locationContainer}>
            <MapPin size={12} color={colors.gray[400]} />
            <Text style={styles.locationText}>{item.location?.city}</Text>
          </View>
        </View>

        <View style={styles.providerFooter}>
          <Text style={styles.experienceText}>{item.experience} years exp</Text>
          <View style={[
            styles.availabilityBadge,
            item.isAvailable ? styles.availableBadge : styles.unavailableBadge
          ]}>
            <Text style={[
              styles.availabilityText,
              item.isAvailable ? styles.availableText : styles.unavailableText
            ]}>
              {item.isAvailable ? 'Available' : 'Busy'}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <Search size={20} color={colors.gray[400]} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search services or providers..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={colors.gray[400]}
          />
        </View>
        
        <TouchableOpacity style={styles.filterButton}>
          <Filter size={20} color={colors.gray[600]} />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.mapButton}
          onPress={() => setShowMap(!showMap)}
        >
          <Map size={20} color={showMap ? colors.primary[600] : colors.gray[600]} />
        </TouchableOpacity>
      </View>

      {/* Categories */}
      <FlatList
        data={SERVICE_CATEGORIES}
        renderItem={renderCategory}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesContainer}
        style={styles.categoriesList}
      />

      {/* Results Count */}
      <View style={styles.resultsHeader}>
        <Text style={styles.resultsCount}>
          {providers.length} providers found
        </Text>
        <TouchableOpacity>
          <Text style={styles.sortText}>Sort by distance</Text>
        </TouchableOpacity>
      </View>

      {/* Providers List */}
      <FlatList
        data={providers}
        renderItem={renderProvider}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.providersList}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    gap: spacing.md,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing.lg,
    gap: spacing.md,
  },
  searchInput: {
    flex: 1,
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[900],
    paddingVertical: spacing.md,
  },
  filterButton: {
    padding: spacing.md,
    borderRadius: borderRadius.md,
    backgroundColor: colors.gray[50],
  },
  mapButton: {
    padding: spacing.md,
    borderRadius: borderRadius.md,
    backgroundColor: colors.gray[50],
  },
  categoriesList: {
    maxHeight: 60,
  },
  categoriesContainer: {
    paddingHorizontal: spacing.xl,
    gap: spacing.md,
  },
  categoryChip: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.full,
    backgroundColor: colors.gray[100],
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  categoryChipActive: {
    backgroundColor: colors.primary[600],
    borderColor: colors.primary[600],
  },
  categoryChipText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Medium',
    color: colors.gray[700],
  },
  categoryChipTextActive: {
    color: colors.white,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
  },
  resultsCount: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  sortText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Medium',
    color: colors.primary[600],
  },
  providersList: {
    paddingHorizontal: spacing.xl,
    paddingBottom: spacing['3xl'],
  },
  providerCard: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    marginBottom: spacing.lg,
    borderWidth: 1,
    borderColor: colors.gray[200],
    overflow: 'hidden',
    position: 'relative',
  },
  providerImage: {
    width: '100%',
    height: 120,
  },
  favoriteButton: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  providerInfo: {
    padding: spacing.lg,
  },
  providerName: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  providerTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Regular',
    color: colors.gray[600],
    marginBottom: spacing.md,
  },
  providerMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  ratingText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-SemiBold',
    color: colors.gray[900],
  },
  reviewCount: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  locationText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Regular',
    color: colors.gray[500],
  },
  providerFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  experienceText: {
    fontSize: typography.fontSize.sm,
    fontFamily: 'Inter-Medium',
    color: colors.gray[600],
  },
  availabilityBadge: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.full,
  },
  availableBadge: {
    backgroundColor: colors.success[100],
  },
  unavailableBadge: {
    backgroundColor: colors.gray[100],
  },
  availabilityText: {
    fontSize: typography.fontSize.xs,
    fontFamily: 'Inter-SemiBold',
  },
  availableText: {
    color: colors.success[700],
  },
  unavailableText: {
    color: colors.gray[600],
  },
});