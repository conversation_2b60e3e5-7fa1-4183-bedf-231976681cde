import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, typography, borderRadius } from '@/constants/theme';
import Button from '@/components/common/Button';

export default function WelcomeScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[colors.primary[600], colors.primary[700]]}
        style={styles.gradient}
      >
        <View style={styles.content}>
          <View style={styles.logoContainer}>
            <Image
              source={{ uri: 'https://images.pexels.com/photos/1458318/pexels-photo-1458318.jpeg?auto=compress&cs=tinysrgb&w=200' }}
              style={styles.logo}
            />
            <Text style={styles.title}>Earan</Text>
            <Text style={styles.subtitle}>
              Connect with skilled artisans in your area
            </Text>
          </View>

          <View style={styles.features}>
            <View style={styles.feature}>
              <Text style={styles.featureText}>🔧 Expert Craftsmen</Text>
            </View>
            <View style={styles.feature}>
              <Text style={styles.featureText}>⚡ Quick Booking</Text>
            </View>
            <View style={styles.feature}>
              <Text style={styles.featureText}>📍 Local Services</Text>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              variant="secondary"
              size="lg"
              onPress={() => router.push('/(auth)/phone')}
              fullWidth
            >
              Get Started
            </Button>
            
            <TouchableOpacity
              style={styles.loginButton}
              onPress={() => router.push('/(auth)/phone')}
            >
              <Text style={styles.loginText}>Already have an account? Sign In</Text>
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing['3xl'],
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: spacing['4xl'],
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: spacing.lg,
  },
  title: {
    fontSize: typography.fontSize['4xl'],
    fontFamily: 'Inter-Bold',
    color: colors.white,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: 'Inter-Regular',
    color: colors.primary[100],
    textAlign: 'center',
    lineHeight: 24,
  },
  features: {
    alignItems: 'center',
    gap: spacing.lg,
  },
  feature: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.lg,
    backdropFilter: 'blur(10px)',
  },
  featureText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Medium',
    color: colors.white,
  },
  buttonContainer: {
    gap: spacing.lg,
  },
  loginButton: {
    alignItems: 'center',
    paddingVertical: spacing.md,
  },
  loginText: {
    fontSize: typography.fontSize.base,
    fontFamily: 'Inter-Medium',
    color: colors.primary[100],
  },
});